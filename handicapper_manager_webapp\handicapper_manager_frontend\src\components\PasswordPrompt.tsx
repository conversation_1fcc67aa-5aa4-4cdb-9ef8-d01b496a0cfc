import React, { useState } from "react";
import Overlay from "./Overlay";

interface PasswordPromptProps {
  isOpen: boolean;
  title: string;
  confirmLabel?: string;
  onSubmit: (password: string) => void;
  onClose: () => void;
}

const PasswordPrompt: React.FC<PasswordPromptProps> = ({
  isOpen,
  title,
  confirmLabel,
  onSubmit,
  onClose,
}) => {
  const [password, setPassword] = useState("");
  if (!isOpen) return null;

  const submit = () => {
    onSubmit(password);
    setPassword("");
  };

  return (
    <Overlay onClose={onClose}>
      <div className="text-white max-w-xs text-center">
        <h2 className="text-2xl font-bold mb-4 text-[#58C612]">{title}</h2>
        <input
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          placeholder="Admin Password"
          className="w-full mb-6 p-2 rounded-lg bg-[#233e6c] outline-none focus:ring-2 focus:ring-[#58C612]"
        />
        <div className="flex justify-center gap-4">
          <button
            onClick={onClose}
            className="px-6 py-2 rounded-lg bg-red-600 hover:bg-red-500 transition-all duration-300 cursor-pointer"
          >
            Cancel
          </button>
          <button
            onClick={submit}
            className="px-6 py-2 rounded-lg bg-[#58C612] text-black font-bold hover:bg-[#449e10] transition-all duration-300 cursor-pointer"
          >
            {confirmLabel || "Submit"}
          </button>
        </div>
      </div>
    </Overlay>
  );
};

export default PasswordPrompt;
