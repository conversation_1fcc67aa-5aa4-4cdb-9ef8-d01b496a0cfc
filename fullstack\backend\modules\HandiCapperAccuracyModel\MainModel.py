from fullstack.backend.modules.HandiCapperAccuracyModel.models.LogisticRegressionModel import (
    LogisticRegressionModel,
)
from fullstack.backend.modules.HandiCapperAccuracyModel.models.BayesianLogisticPredictor import (
    BayesianLogisticPredictor,
)
from fullstack.backend.modules.HandiCapperAccuracyModel.models.KDECDFShifter import (
    KDECDFShifter,
)
from fullstack.backend.modules.HandiCapperAccuracyModel.util.PlotUtils import (
    PlotUtils,
)
from fullstack.backend.modules.HandiCapperAccuracyModel.database.AccuracyModelDatabase import (
    AccuracyModelDatabase,
)
from fullstack.backend.modules.HandiCapperAccuracyModel.util.StatUtils import StatUtils
from fullstack.backend.data_science_modules.kde_distributions_port import import_distribution



class MainModel:
    def __init__(self):
        self.logistic_regression_model = LogisticRegressionModel()
        self.bayesian_logistic_predictor = BayesianLogisticPredictor()
        self.kde_cdf_shifter = KDECDFShifter()
        self.plot_utils = PlotUtils()
        self.stat_utils = StatUtils()
        self.db = AccuracyModelDatabase()
        return

    def main_model(self, event_id):
        print(f"🔍 Starting model pipeline for Event ID: {event_id}")

        # === Step 1: Train Logistic Model ===
        logistic_model, expert_names = (
            self.logistic_regression_model.train_logistic_model()
        )

        # === Step 2: Predict Bayesian + Logistic + Combined ===
        result = self.bayesian_logistic_predictor.run_prediction(
            event_id, logistic_model, expert_names
        )
        if "error" in result:
            return result
        print("✅ Prediction complete.")

        # === Step 3: Get Event Metadata for KDE model ===
        metadata = self.get_event_metadata(event_id)
        if not metadata:
            print("❌ Event metadata not found.")
            return {"error": "Event metadata not found."}

        # TODO: better comment
        # previous of Step 4: fetch data that step 4 needs
        team_name = self.db.get_team_name(
            player_name=metadata["player_name"],
            league=metadata["league"],
            target_date=metadata["game_date"],
        )
        if not team_name:
            return None

        expert_df = self.get_expert_prediction_for_team_date(
            team_name=team_name,
            league=metadata["league"],
            game_date=metadata["game_date"]
        )
        stat_column, original_data = self.stat_utils.get_stat_distribution(
            team_name=team_name,
            game_date=metadata["game_date"],
            player_name=metadata["player_name"],
            stat_type=metadata["stat_type"].lower(),
            league=metadata["league"],
        )
        # === Step 4: Compute Team KDE CDF with Mean-Shift ===
        kde_data = self.kde_cdf_shifter.compute_kde_probabilities(
            expert_df, stat_column, original_data, metadata, logistic_model, expert_names
        )
        result.update(kde_data)

        # === Step 5: Display Final Result ===
        self.display_final_result(result)
        packed_data = self.plot_utils.pack_distribution_data(
            sample_range=kde_data["sample_range"],
            pdf=kde_data["pdf"],
            cdf=kde_data["cdf"]
        )
        
        #Imports prediction into database for later use
        import_distribution(
            event_id=event_id,
            player_name=metadata["player_name"],
            stat_type=metadata["stat_type"],
            game_date=metadata["game_date"],
            pdf_json=packed_data["pdf_json"],
            cdf_json=packed_data["cdf_json"],
            odds_pdf_json=packed_data["oods_pdf_json"],
            odds_cdf_json=packed_data["oods_cdf_json"],
        )

        self.logistic_regression_model.close()
        self.bayesian_logistic_predictor.close()
        self.kde_cdf_shifter.close()
        return result

    def display_final_result(self, result: dict):
        print("\n📊 Final Combined Prediction Result:")
        for k, v in result.items():
            print(f" - {k}: {v}")

    def get_expert_prediction_for_team_date(self, team_name, league, game_date):
        return self.db.fetch_expert_predictions_for_team_date(team_name, league, game_date)

    def get_event_metadata(self, event_id):
        print("📦 Fetching event metadata for KDE modeling...")
        metadata = self.db.fetch_event_metadata(event_id)
        if metadata:
            print(
                f"📅 Event Date: {metadata['game_date']}, League: {metadata['league']}"
            )
            print(
                f"🎯 Target Player: {metadata['player_name']}, Stat: {metadata['stat_type'].lower()}, Threshold: {metadata['stat_threshold']}"
            )
        return metadata

    # def _test_display_packed_data(self, packed_data):
    #     import json
    #     print("\n📦 Packed Distribution Data:")

    #     for key in ["pdf_json", "cdf_json", "oods_pdf_json", "oods_cdf_json"]:
    #         print(f"\n🔹 {key}:")
    #         parsed = json.loads(packed_data[key])
    #         preview = dict(list(parsed.items())[:100000])  # 상위 10개만 미리보기
    #         print(json.dumps(preview, indent=2))
