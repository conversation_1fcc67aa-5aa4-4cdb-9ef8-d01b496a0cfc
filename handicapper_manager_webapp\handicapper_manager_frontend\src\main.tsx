import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App";
import StartupScreen from "./components/StartupScreen";
import "./index.css";

const Root = () => {
  const [showStartup, setShowStartup] = React.useState(true);

  return (
    <>
      {showStartup && (
        <StartupScreen
          videoSrc="/project_parlay_startup_anim.mp4"
          onAnimationComplete={() => setShowStartup(false)}
        />
      )}
      {!showStartup && <App />}
    </>
  );
};

ReactDOM.createRoot(document.getElementById("root") as HTMLElement).render(
  <React.StrictMode>
    <Root />
  </React.StrictMode>
);
