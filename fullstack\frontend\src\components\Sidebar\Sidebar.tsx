import React from "react";
import { Hi<PERSON><PERSON><PERSON>, HiCog6Tooth, HiXMark } from "react-icons/hi2";
import { useSidebar } from "../../contexts/SidebarContext";
import { useAuth } from "../../contexts/AuthContext";
import FavoriteHandicappersDropdown from "./FavoriteHandicappersDropdown";

const Sidebar: React.FC = () => {
  const { isOpen, closeSidebar } = useSidebar();
  const { navigateToView } = useAuth();

  const navItems = [
    {
      label: "Parlay Engine",
      icon: HiSparkles,
      action: () => {
        navigateToView("home");
        closeSidebar();
      },
    },
    {
      label: "Settings",
      icon: HiCog6Tooth,
      action: () => {
        closeSidebar();
      },
    },
  ];

  return (
    <>
      {/* Overlay */}
      <div
        className={`fixed inset-0 bg-black/40 transition-opacity z-40 ${
          isOpen
            ? "opacity-100 visible duration-300"
            : "opacity-0 invisible pointer-events-none duration-0"
        }`}
        onClick={closeSidebar}
      />

      {/* Sidebar panel */}
      <aside
        className={`fixed top-0 left-0 h-full w-full sm:w-64 bg-[#17305b] shadow-xl z-50 transform transition-transform flex flex-col ${
          isOpen ? "translate-x-0 duration-300" : "-translate-x-full duration-0"
        }`}
      >
        <div className="flex items-center justify-between px-6 py-4 border-b border-gray-600">
          <span className="text-white font-semibold text-lg">
            Project Parlay
          </span>
          <HiXMark
            className="w-6 h-6 text-white hover:text-gray-300 cursor-pointer"
            onClick={closeSidebar}
          />
        </div>
        <nav className="flex-1 overflow-y-auto divide-y divide-gray-600">
          {navItems.map(({ label, icon: Icon, action }) => (
            <button
              key={label}
              onClick={action}
              className="group w-full flex items-center gap-4 px-6 py-4 text-white transition-colors hover:bg-[#1a2d54] cursor-pointer"
            >
              <Icon className="flex-shrink-0 w-6 h-6 sm:w-5 sm:h-5 text-gray-300 transition-colors group-hover:text-[#58C612]" />
              <span className="text-base font-semibold sm:text-sm whitespace-nowrap transition-colors group-hover:text-[#58C612]">
                {label}
              </span>
            </button>
          ))}
          <FavoriteHandicappersDropdown />
        </nav>
      </aside>
    </>
  );
};

export default Sidebar;
