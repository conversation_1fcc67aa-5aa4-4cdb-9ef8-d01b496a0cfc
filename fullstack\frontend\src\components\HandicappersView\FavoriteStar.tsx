import React from "react";
import { HiStar, HiOutlineStar } from "react-icons/hi2";

interface FavoriteStarProps {
  isFavorite: boolean;
  onToggle: () => void;
}

function FavoriteStar({ isFavorite, onToggle }: FavoriteStarProps) {
  return (
    <div
      className="flex flex-col items-center justify-center gap-0 cursor-pointer w-20 h-20 hover:bg-white/10 hover:rounded-lg transition-all duration-200"
      onClick={onToggle}
    >
      {isFavorite ? (
        <HiStar className="w-12 h-12 text-red-500" />
      ) : (
        <HiOutlineStar className="w-12 h-12 text-white hover:text-red-500 transition-colors duration-200" />
      )}
      {isFavorite && (
        <span className="text-red-500 text-md font-medium leading-tight">
          Favorite
        </span>
      )}
    </div>
  );
}

export default FavoriteStar;
