import React from "react";

interface OverlayProps {
  children: React.ReactNode;
  onClose?: () => void;
}

const Overlay: React.FC<OverlayProps> = ({ children }) => {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm">
      <div
        className="relative bg-[#061844] p-6 rounded-xl shadow-xl border border-gray-700"
        onClick={(e) => e.stopPropagation()} // Prevent closing when clicking inside the modal
      >
        {children}
      </div>
    </div>
  );
};

export default Overlay;
