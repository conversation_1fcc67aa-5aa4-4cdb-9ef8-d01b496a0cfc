import React, {
  createContext,
  useState,
  useContext,
  ReactNode,
  useEffect,
  useCallback,
  useRef,
} from "react";
import SmartCache from "../utils/cache";

export interface SelectedPick {
  id: string; // Unique identifier combining source and pick ID
  sourceType: "pick" | "handicapper"; // Where the pick came from
  sourceId: number; // Original pick/handicapper ID
  playerName: string;
  playerNumber: string;
  betType: string;
  gameInfo: string;
  confidence?: number;
  handicapperNames?: string[]; // List of associated handicappers for this pick
  handicapperName?: string; // For picks from handicappers
  addedAt: number; // Timestamp when added
}

interface PicksContextType {
  selectedPicks: SelectedPick[];
  addPick: (pick: Omit<SelectedPick, "id" | "addedAt">) => void;
  removePick: (pickId: string) => void;
  clearAllPicks: () => void;
  isPickSelected: (
    sourceType: "pick" | "handicapper",
    sourceId: number,
    handicapperName?: string
  ) => boolean;
  getPicksBySource: (sourceType: "pick" | "handicapper") => SelectedPick[];
  getPicks: () => SelectedPick[];
  totalPicksCount: number;
}

const PicksContext = createContext<PicksContextType | undefined>(undefined);

// Create cache instance for My Picks with longer persistence
const picksCache = new SmartCache("my_picks", {
  freshDuration: 24 * 60 * 60 * 1000, // 24 hours fresh
  maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days max age
  version: "1.0.0",
});

export const PicksProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  // Initialize picks from cache synchronously (avoids empty state flash)
  const [selectedPicks, setSelectedPicks] = useState<SelectedPick[]>(() => {
    const { data } = picksCache.get<SelectedPick[]>("user_picks");
    if (data && Array.isArray(data)) {
      console.log("PicksContext: Initialized", data.length, "picks from cache");
      return data;
    }
    return [];
  });

  // After initial mount, persist every change (including clear)
  const isFirstRender = useRef(true);
  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      // No need to save—data already matches cache
      return;
    }

    picksCache.set(selectedPicks, "user_picks");
    console.log("PicksContext: Saved", selectedPicks.length, "picks to cache");
  }, [selectedPicks]);

  // Generate unique ID for a pick
  const generatePickId = useCallback(
    (
      sourceType: "pick" | "handicapper",
      sourceId: number,
      handicapperName?: string
    ): string => {
      if (sourceType === "handicapper" && handicapperName) {
        return `${sourceType}_${sourceId}_${handicapperName.replace(
          /\s+/g,
          "_"
        )}`;
      }
      return `${sourceType}_${sourceId}`;
    },
    []
  );

  // Add a pick to the selection
  const addPick = useCallback(
    (pick: Omit<SelectedPick, "id" | "addedAt">) => {
      const pickId = generatePickId(
        pick.sourceType,
        pick.sourceId,
        pick.handicapperName
      );

      // Check if pick already exists
      const existingPick = selectedPicks.find((p) => p.id === pickId);
      if (existingPick) {
        console.log("PicksContext: Pick already selected:", pickId);
        return;
      }

      const newPick: SelectedPick = {
        ...pick,
        id: pickId,
        addedAt: Date.now(),
      };

      setSelectedPicks((prev) => [...prev, newPick]);
      console.log("PicksContext: Added pick:", pickId);
    },
    [selectedPicks, generatePickId]
  );

  // Remove a pick by ID
  const removePick = useCallback((pickId: string) => {
    setSelectedPicks((prev) => prev.filter((pick) => pick.id !== pickId));
    console.log("PicksContext: Removed pick:", pickId);
  }, []);

  // Clear all picks
  const clearAllPicks = useCallback(() => {
    setSelectedPicks([]);
    console.log("PicksContext: Cleared all picks");
  }, []);

  // Check if a pick is already selected
  const isPickSelected = useCallback(
    (
      sourceType: "pick" | "handicapper",
      sourceId: number,
      handicapperName?: string
    ): boolean => {
      const pickId = generatePickId(sourceType, sourceId, handicapperName);
      return selectedPicks.some((pick) => pick.id === pickId);
    },
    [selectedPicks, generatePickId]
  );

  // Get picks filtered by source type
  const getPicksBySource = useCallback(
    (sourceType: "pick" | "handicapper"): SelectedPick[] => {
      return selectedPicks.filter((pick) => pick.sourceType === sourceType);
    },
    [selectedPicks]
  );

  // Get all picks (sorted by most recently added)
  const getPicks = useCallback((): SelectedPick[] => {
    return [...selectedPicks].sort((a, b) => b.addedAt - a.addedAt);
  }, [selectedPicks]);

  const contextValue: PicksContextType = {
    selectedPicks,
    addPick,
    removePick,
    clearAllPicks,
    isPickSelected,
    getPicksBySource,
    getPicks,
    totalPicksCount: selectedPicks.length,
  };

  return (
    <PicksContext.Provider value={contextValue}>
      {children}
    </PicksContext.Provider>
  );
};

export const usePicks = (): PicksContextType => {
  const context = useContext(PicksContext);
  if (context === undefined) {
    throw new Error("usePicks must be used within a PicksProvider");
  }
  return context;
};
