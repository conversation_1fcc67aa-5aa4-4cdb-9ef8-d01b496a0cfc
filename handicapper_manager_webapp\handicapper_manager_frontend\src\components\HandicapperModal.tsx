import React, { useState, useEffect } from "react";
import { Handicapper } from "../types/handicapper";
import Overlay from "./Overlay";

type DraftHandicapper = Omit<Handicapper, "id" | "last_updated">;

interface HandicapperModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (newHandicapper: DraftHandicapper & { password: string }) => void;
  initialData?: Partial<DraftHandicapper>;
}

const HandicapperModal: React.FC<HandicapperModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  initialData,
}) => {
  const getInitialState = (): Partial<DraftHandicapper> => ({
    name: "",
    username: "",
    twitter_handle: "",
    instagram_handle: "",
    tiktok_handle: "",
    youtube_handle: "",
    profile_image_url: "",
    accuracy_score: 0,
    avg_units_per_month: 0,
    bio: "",
    pricing: "",
    group_memberships: "",
    estimated_customers: 0,
    followers_count: { twitter: 0, instagram: 0, tiktok: 0, youtube: 0 },
    ...initialData,
  });

  const [draft, setDraft] = useState<Partial<DraftHandicapper>>(
    getInitialState()
  );
  const [password, setPassword] = useState("");

  useEffect(() => {
    if (isOpen) {
      setDraft(getInitialState());
    }
  }, [isOpen, initialData]);

  const numericFieldNames = new Set([
    "accuracy_score",
    "avg_units_per_month",
    "estimated_customers",
  ]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    if (name.startsWith("followers_")) {
      const platform = name.split(
        "_"
      )[1] as keyof Handicapper["followers_count"];
      setDraft((prev) => ({
        ...prev,
        followers_count: {
          twitter: 0,
          instagram: 0,
          tiktok: 0,
          youtube: 0,
          ...prev.followers_count,
          [platform]: parseInt(value.replace(/[^0-9]/g, "")) || 0,
        },
      }));
    } else {
      setDraft((prev) => ({
        ...prev,
        [name]: numericFieldNames.has(name)
          ? parseFloat(value.replace(/[^0-9.]/g, "")) || 0
          : value,
      }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (
      !draft.name ||
      !draft.username ||
      !draft.pricing ||
      draft.accuracy_score === undefined ||
      draft.avg_units_per_month === undefined ||
      !draft.group_memberships
    ) {
      alert("Please fill out all required fields.");
      return;
    }
    if (!draft.twitter_handle && !draft.instagram_handle) {
      alert(
        "At least one social media handle (Twitter or Instagram) is required."
      );
      return;
    }
    if (!password) {
      alert("Password is required.");
      return;
    }

    const finalDraft: DraftHandicapper = {
      name: draft.name,
      username: draft.username,
      twitter_handle: draft.twitter_handle || "",
      instagram_handle: draft.instagram_handle || "",
      tiktok_handle: draft.tiktok_handle || "",
      youtube_handle: draft.youtube_handle || "",
      profile_image_url: draft.profile_image_url || "",
      accuracy_score: draft.accuracy_score,
      avg_units_per_month: draft.avg_units_per_month,
      bio: draft.bio || "",
      pricing: draft.pricing,
      group_memberships: draft.group_memberships,
      estimated_customers: draft.estimated_customers || 0,
      followers_count: draft.followers_count || {
        twitter: 0,
        instagram: 0,
        tiktok: 0,
        youtube: 0,
      },
    };

    onSubmit({ ...finalDraft, password });
  };

  if (!isOpen) return null;

  return (
    <Overlay onClose={onClose}>
      <form
        onSubmit={handleSubmit}
        className="text-white w-[600px] max-w-[90vw]"
      >
        <h2 className="text-2xl font-bold mb-4 text-center text-[#58C612]">
          Create New Handicapper
        </h2>
        <div className="grid grid-cols-2 gap-3 max-h-[75vh] overflow-y-auto handicapper-scroll pr-4 pb-2">
          {/* Form Fields */}
          <div className="col-span-2 mb-2">
            <label
              htmlFor="name"
              className="block text-sm font-medium text-gray-300 mb-1"
            >
              Name
            </label>
            <input
              type="text"
              name="name"
              value={draft.name || ""}
              onChange={handleChange}
              required
              className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
            />
          </div>
          <div className="col-span-2 mb-2">
            <label
              htmlFor="username"
              className="block text-sm font-medium text-gray-300 mb-1"
            >
              Username
            </label>
            <input
              type="text"
              name="username"
              value={draft.username || ""}
              onChange={handleChange}
              placeholder="unique username"
              className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
            />
          </div>
          <div className="mb-2">
            <label
              htmlFor="twitter_handle"
              className="block text-sm font-medium text-gray-300 mb-1"
            >
              X / Twitter Handle
            </label>
            <input
              type="text"
              name="twitter_handle"
              value={draft.twitter_handle || ""}
              onChange={handleChange}
              placeholder="@username"
              className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
            />
          </div>
          <div className="mb-2">
            <label
              htmlFor="instagram_handle"
              className="block text-sm font-medium text-gray-300 mb-1"
            >
              Instagram Handle
            </label>
            <input
              type="text"
              name="instagram_handle"
              value={draft.instagram_handle || ""}
              onChange={handleChange}
              placeholder="@username"
              className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
            />
          </div>
          <div className="mb-2">
            <label
              htmlFor="tiktok_handle"
              className="block text-sm font-medium text-gray-300 mb-1"
            >
              TikTok Handle
            </label>
            <input
              type="text"
              name="tiktok_handle"
              value={draft.tiktok_handle || ""}
              onChange={handleChange}
              placeholder="@username"
              className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
            />
          </div>
          <div className="mb-2">
            <label
              htmlFor="youtube_handle"
              className="block text-sm font-medium text-gray-300 mb-1"
            >
              YouTube Channel
            </label>
            <input
              type="text"
              name="youtube_handle"
              value={draft.youtube_handle || ""}
              onChange={handleChange}
              placeholder="link or handle"
              className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
            />
          </div>
          <div className="col-span-2 mb-2">
            <label
              htmlFor="profile_image_url"
              className="block text-sm font-medium text-gray-300 mb-1"
            >
              Profile Image URL
            </label>
            <input
              type="text"
              name="profile_image_url"
              value={draft.profile_image_url || ""}
              onChange={handleChange}
              placeholder="https://...jpg"
              className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
            />
          </div>
          <div className="mb-2">
            <label
              htmlFor="accuracy_score"
              className="block text-sm font-medium text-gray-300 mb-1"
            >
              Accuracy %
            </label>
            <input
              type="text"
              name="accuracy_score"
              value={draft.accuracy_score || 0}
              onChange={handleChange}
              placeholder="e.g. 53 (0-100)"
              className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
            />
          </div>
          <div className="mb-2">
            <label
              htmlFor="avg_units_per_month"
              className="block text-sm font-medium text-gray-300 mb-1"
            >
              Units/Month
            </label>
            <input
              type="text"
              name="avg_units_per_month"
              step="0.01"
              value={draft.avg_units_per_month || 0}
              onChange={handleChange}
              placeholder="e.g. 12.5"
              className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
            />
          </div>
          <div className="col-span-2 mb-2">
            <label
              htmlFor="bio"
              className="block text-sm font-medium text-gray-300 mb-1"
            >
              Bio
            </label>
            <input
              type="text"
              name="bio"
              value={draft.bio || ""}
              onChange={handleChange}
              placeholder="short bio..."
              className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
            />
          </div>
          <div className="col-span-2 mb-2">
            <label
              htmlFor="pricing"
              className="block text-sm font-medium text-gray-300 mb-1"
            >
              Pricing
            </label>
            <input
              type="text"
              name="pricing"
              value={draft.pricing || ""}
              onChange={handleChange}
              placeholder="$100/month"
              className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
            />
          </div>
          <div className="col-span-2 mb-2">
            <label
              htmlFor="group_memberships"
              className="block text-sm font-medium text-gray-300 mb-1"
            >
              Group
            </label>
            <input
              type="text"
              name="group_memberships"
              value={draft.group_memberships || ""}
              onChange={handleChange}
              placeholder="group name"
              className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
            />
          </div>
          <div className="mb-2">
            <label
              htmlFor="estimated_customers"
              className="block text-sm font-medium text-gray-300 mb-1"
            >
              Estimated Customers
            </label>
            <input
              type="text"
              name="estimated_customers"
              value={draft.estimated_customers || 0}
              onChange={handleChange}
              placeholder="e.g. 450"
              className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
            />
          </div>
          <div className="col-span-2 font-semibold text-gray-300 mt-3 mb-2 pb-1 border-b border-gray-600">
            Followers Count
          </div>
          <div className="mb-2">
            <label
              htmlFor="followers_twitter"
              className="block text-sm font-medium text-gray-300 mb-1"
            >
              Twitter
            </label>
            <input
              type="text"
              name="followers_twitter"
              value={draft.followers_count?.twitter || 0}
              onChange={handleChange}
              className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
            />
          </div>
          <div className="mb-2">
            <label
              htmlFor="followers_instagram"
              className="block text-sm font-medium text-gray-300 mb-1"
            >
              Instagram
            </label>
            <input
              type="text"
              name="followers_instagram"
              value={draft.followers_count?.instagram || 0}
              onChange={handleChange}
              className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
            />
          </div>
          <div className="mb-2">
            <label
              htmlFor="followers_tiktok"
              className="block text-sm font-medium text-gray-300 mb-1"
            >
              TikTok
            </label>
            <input
              type="text"
              name="followers_tiktok"
              value={draft.followers_count?.tiktok || 0}
              onChange={handleChange}
              className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
            />
          </div>
          <div className="mb-2">
            <label
              htmlFor="followers_youtube"
              className="block text-sm font-medium text-gray-300 mb-1"
            >
              YouTube
            </label>
            <input
              type="text"
              name="followers_youtube"
              value={draft.followers_count?.youtube || 0}
              onChange={handleChange}
              className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
            />
          </div>
          <div className="col-span-2 mb-4 mt-3 pt-3 border-t border-gray-600">
            <label
              htmlFor="password"
              className="block text-sm font-medium text-gray-300 mb-1"
            >
              Password
            </label>
            <input
              type="password"
              name="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
            />
          </div>
        </div>
        <div className="flex justify-end gap-4 mt-6">
          <button
            type="button"
            onClick={onClose}
            className="px-6 py-2 rounded-lg bg-red-600 hover:bg-red-500 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-red-400 cursor-pointer"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-6 py-2 rounded-lg bg-[#58C612] text-black font-bold hover:bg-green-400 transition-colors cursor-pointer"
          >
            Submit
          </button>
        </div>
      </form>
    </Overlay>
  );
};

export default HandicapperModal;
