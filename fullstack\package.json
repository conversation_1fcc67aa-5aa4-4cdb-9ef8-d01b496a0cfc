{"name": "fullstack", "version": "1.0.0", "main": "index.js", "scripts": {"setup": "npm install && npm --prefix frontend install && pip install -r backend/requirements.txt", "dev": "concurrently \"npm --prefix frontend run dev\" \"python backend/app.py\"", "dev:backend": "python backend/app.py", "dev:frontend": "npm --prefix frontend run dev", "build:frontend": "npm --prefix frontend run build", "build:backend": "pip install -r backend/requirements.txt", "build": "npm --prefix frontend run build", "serve:frontend": "npm --prefix frontend run serve", "start": "concurrently \"npm run serve:frontend\" \"npm run dev:backend\""}, "author": "", "license": "ISC", "description": "", "devDependencies": {"concurrently": "^9.1.2"}, "dependencies": {"dotenv": "^16.5.0"}}