import React, { useState } from "react";
import { HiStar, HiChevronRight, HiUser } from "react-icons/hi2";
import { useFavorites } from "../../contexts/FavoritesContext";
import { useHandicapperProfile } from "../../contexts/HandicapperProfileContext";
import { useAuth } from "../../contexts/AuthContext";
import { useSidebar } from "../../contexts/SidebarContext";

const FavoriteHandicappersDropdown: React.FC = () => {
  const [open, setOpen] = useState(false);
  const { favoriteHandicappers } = useFavorites();
  const { selectHandicapper } = useHandicapperProfile();
  const { navigateToView } = useAuth();
  const { closeSidebar } = useSidebar();

  const handleNavigate = (id: number) => {
    selectHandicapper(id);
    navigateToView("handicapperProfile");
    closeSidebar();
  };

  return (
    <div>
      <button
        onClick={() => setOpen(!open)}
        className="group w-full flex items-center gap-4 px-6 py-4 text-white transition-colors hover:bg-[#1a2d54] cursor-pointer"
      >
        <HiStar className="w-6 h-6 text-gray-300 group-hover:text-[#58C612] transition-colors" />
        <span className="flex-1 text-base font-semibold sm:text-sm text-left group-hover:text-[#58C612] transition-colors">
          My Favorite Handicappers
        </span>
        <HiChevronRight
          className={`w-4 h-4 transition-transform duration-300 text-gray-300 group-hover:text-[#58C612] ${
            open ? "rotate-90" : ""
          }`}
        />
      </button>
      <div
        className={`overflow-hidden transition-[max-height] duration-300 bg-[#17305b] ${
          open ? "max-h-96" : "max-h-0"
        }`}
      >
        {favoriteHandicappers.length > 0 ? (
          favoriteHandicappers.map((h) => (
            <button
              key={h.id}
              onClick={() => handleNavigate(h.id)}
              className="w-full flex items-center gap-4 pl-10 pr-6 py-3 text-white text-sm hover:bg-[#1a2d54] hover:text-[#58C612] text-left transition-all duration-200 cursor-pointer group"
            >
              <HiUser className="w-5 h-5 text-gray-400 group-hover:text-[#58C612] transition-colors flex-shrink-0" />
              <span className="transition-colors">{h.name}</span>
            </button>
          ))
        ) : (
          <div className="flex items-center gap-4 pl-10 pr-6 py-3 text-gray-400 text-sm">
            <HiUser className="w-5 h-5 text-gray-500 flex-shrink-0" />
            <span>No favorites yet</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default FavoriteHandicappersDropdown;
