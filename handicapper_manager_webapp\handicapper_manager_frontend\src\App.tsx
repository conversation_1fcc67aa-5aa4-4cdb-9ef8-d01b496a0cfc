import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import Homepage from './pages/Homepage';
import ProfilePage from './pages/ProfilePage';
// import AddEditProfileForm from './pages/AddEditProfileForm'; // Placeholder
// import BulkUploadPage from './pages/BulkUploadPage'; // Placeholder

const App: React.FC = () => {
  return (
    <Router>
      {/* Navbar removed per request */}
      <Routes>
        <Route path="/" element={<Homepage />} />
        <Route path="/profile/:id" element={<ProfilePage />} />
        {/* <Route path="/add" element={<AddEditProfileForm />} /> */}
        {/* <Route path="/edit/:id" element={<AddEditProfileForm />} /> */}
        {/* <Route path="/bulk-upload" element={<BulkUploadPage />} /> */}
      </Routes>
    </Router>
  );
};

export default App;
