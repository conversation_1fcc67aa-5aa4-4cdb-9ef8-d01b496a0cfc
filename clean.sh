#!/bin/bash
cd fullstack
echo "Cleaning backend..."
cd backend
if [ -d "venv" ]; then
    rm -rf venv
fi
cd ..
echo "Cleaning frontend..."
cd frontend
if [ -d "node_modules" ]; then
    rm -rf node_modules
fi
if [ -d "dist" ]; then
    rm -rf dist
fi
if [ -f "package-lock.json" ]; then
    rm package-lock.json
fi
cd ..
if [ -d "node_modules" ]; then
    rm -rf node_modules
fi
if [ -f "package-lock.json" ]; then
    rm package-lock.json
fi
echo "Clean complete"