{"name": "handicapper_manager_webapp", "version": "1.0.0", "main": "index.js", "scripts": {"setup": "npm install && npm --prefix handicapper_manager_frontend install && pip install -r handicapper_manager_backend/requirements.txt", "dev": "concurrently \"npm --prefix handicapper_manager_frontend run dev\" \"python handicapper_manager_backend/app.py\"", "dev:backend": "python handicapper_manager_backend/app.py", "dev:frontend": "npm --prefix handicapper_manager_frontend run dev", "build:frontend": "npm --prefix handicapper_manager_frontend run build", "build:backend": "pip install -r handicapper_manager_backend/requirements.txt", "build": "npm --prefix handicapper_manager_frontend run build", "serve:frontend": "npm --prefix handicapper_manager_frontend run serve", "start:backend": "cd handicapper_manager_backend && python app.py", "start": "bash start-production.sh"}, "author": "", "license": "MIT", "description": "", "devDependencies": {"concurrently": "^9.1.2"}}