process.on("uncaughtException", (err, origin) => {
  console.error("Caught exception:", err);
  console.error("Exception origin:", origin);
  process.exit(1);
});

import express from "express";
import path from "path";
import { fileURLToPath } from "url";
import { createProxyMiddleware } from "http-proxy-middleware";

console.log("Starting production server...");

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log("__dirname resolved to:", __dirname);

const app = express();

const distPath = path.join(__dirname, "..", "..", "dist");
console.log("Serving static files from:", distPath);

const backendTarget = "http://127.0.0.1:5000";
console.log("Proxying API requests to:", backendTarget);

app.use(
  "/handicappers",
  createProxyMiddleware({
    target: backendTarget,
    changeOrigin: true,
    timeout: 10000,
    pathRewrite: (path) => "/handicappers" + (path === "/" ? "" : path),
  })
);

app.use(
  "/health",
  createProxyMiddleware({
    target: backendTarget,
    changeOrigin: true,
    timeout: 10000,
    pathRewrite: { "^/health": "/health" },
  })
);

app.use(express.static(distPath));

app.get("*", (_, res) => {
  res.sendFile(path.join(distPath, "index.html"));
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Frontend server running on port ${PORT}`);
  console.log(`Proxying API calls to backend at ${backendTarget}`);
});

// server.on('error', (err) => {
//   console.error('Server error:', err);
//   process.exit(1);
// });

// export default app;
