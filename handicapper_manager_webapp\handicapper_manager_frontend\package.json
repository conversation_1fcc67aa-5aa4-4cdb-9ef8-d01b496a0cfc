{"name": "handicapper_manager_frontend", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build && tsc --project tsconfig.server.json", "build:server": "tsc --project tsconfig.server.json", "clean-install": "rm -rf node_modules && npm install", "serve": "npm run build && node dist-server/server/server.js", "test:ts": "node --loader ts-node/esm test.ts"}, "dependencies": {"@tailwindcss/vite": "^4.1.7", "autoprefixer": "^10.4.21", "express": "^4.18.2", "handicapper_manager_webapp": "file:..", "http-proxy-middleware": "^3.0.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.0", "tailwind-scrollbar": "^4.0.2", "tailwindcss": "^4.1.7"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/postcss": "^4.1.10", "@types/express": "^4.17.21", "@types/node": "^20.8.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "ts-node": "^10.9.2", "typescript": "^5.4.0", "vite": "^6.3.5"}, "proxy": "http://127.0.0.1:5000"}