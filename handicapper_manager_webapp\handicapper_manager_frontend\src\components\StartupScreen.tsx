import React, { useEffect, useRef, useState } from 'react';

interface StartupScreenProps {
  onAnimationComplete: () => void;
  videoSrc: string;
}

// Plays a startup MP4 once at 1.5× speed, then fades out.
const StartupScreen: React.FC<StartupScreenProps> = ({ onAnimationComplete, videoSrc }) => {
  const [isFadingOut, setIsFadingOut] = useState(false);
  const [show, setShow] = useState(true);
  const videoRef = useRef<HTMLVideoElement>(null);

  // Kick-off playback at faster speed once mounted
  useEffect(() => {
    if (show && videoRef.current) {
      videoRef.current.playbackRate = 1.5;
      videoRef.current.play().catch(() => {/* ignore autoplay errors */});
    }
  }, [show]);

  const beginFadeOut = () => {
    if (isFadingOut) return; // prevent double triggers
    setIsFadingOut(true);
    setTimeout(() => {
      setShow(false);
      onAnimationComplete();
    }, 800); // keep in sync with CSS duration below
  };

  const handleVideoError = () => beginFadeOut();

  if (!show && !isFadingOut) return null;

  return (
    <div
      className={`fixed inset-0 z-[10000] flex items-center justify-center bg-[#061844] transition-opacity duration-800 ease-in-out ${
        isFadingOut ? 'opacity-0 pointer-events-none' : 'opacity-100'
      }`}
    >
      <video
        ref={videoRef}
        src={videoSrc}
        muted
        playsInline
        onEnded={beginFadeOut}
        onError={handleVideoError}
        className="w-full h-full object-contain"
      />
    </div>
  );
};

export default StartupScreen;
