#!/usr/bin/env python3
"""
Test script to verify the event ID generation logic works correctly
"""

import sys
import os

# Add the project root to the path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

# Add the backend directory to the path
backend_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(backend_dir)

# Test the function directly without imports
def generate_admin_event_id(event_date, league, pick_type, team_a, team_b, player_name=None, stat_threshold=None, stat_type=None):
    """
    Generate consistent event IDs for admin form in the format: YYYY-MM-DD-LEAGUE-IDENTIFIER

    For player stat picks: YYYY-MM-DD-NBA-PLAYERNAME##.#STATTYPE
    For team matchups: YYYY-MM-DD-NBA-TEAMA-TEAMB
    """
    # Ensure all components are uppercase
    league = league.upper()

    if pick_type == "StatOverUnder" and player_name and stat_threshold is not None and stat_type:
        # Player stat pick format: YYYY-MM-DD-NBA-PLAYERNAME##.#STATTYPE
        # Clean player name: remove spaces, convert to uppercase
        clean_player_name = player_name.replace(" ", "").upper()
        clean_stat_type = stat_type.replace(" ", "").upper()

        # Format stat threshold to ensure proper decimal format
        if isinstance(stat_threshold, (int, float)):
            threshold_str = f"{stat_threshold:.1f}" if stat_threshold % 1 != 0 else f"{int(stat_threshold)}.0"
        else:
            threshold_str = str(stat_threshold)

        event_id = f"{event_date}-{league}-{clean_player_name}{threshold_str}{clean_stat_type}"
    else:
        # Team matchup format: YYYY-MM-DD-NBA-TEAMA-TEAMB
        clean_team_a = team_a.replace(" ", "").upper()
        clean_team_b = team_b.replace(" ", "").upper()
        event_id = f"{event_date}-{league}-{clean_team_a}-{clean_team_b}"

    return event_id

def test_event_id_generation():
    """Test various scenarios for event ID generation"""
    
    print("🧪 Testing Event ID Generation Logic")
    print("=" * 50)
    
    # Test 1: Player stat pick
    print("\n1. Testing Player Stat Pick:")
    event_id_1 = generate_admin_event_id(
        event_date="2025-05-28",
        league="NBA",
        pick_type="StatOverUnder",
        team_a="Lakers",
        team_b="Warriors",
        player_name="J Brunson",
        stat_threshold=21.5,
        stat_type="PTS"
    )
    expected_1 = "2025-05-28-NBA-JBRUNSON21.5PTS"
    print(f"Generated: {event_id_1}")
    print(f"Expected:  {expected_1}")
    print(f"✅ Match: {event_id_1 == expected_1}")
    
    # Test 2: Player stat pick with integer threshold
    print("\n2. Testing Player Stat Pick (Integer Threshold):")
    event_id_2 = generate_admin_event_id(
        event_date="2025-05-28",
        league="NBA",
        pick_type="StatOverUnder",
        team_a="Lakers",
        team_b="Warriors",
        player_name="K Towns",
        stat_threshold=14,
        stat_type="PTS"
    )
    expected_2 = "2025-05-28-NBA-KTOWNS14.0PTS"
    print(f"Generated: {event_id_2}")
    print(f"Expected:  {expected_2}")
    print(f"✅ Match: {event_id_2 == expected_2}")
    
    # Test 3: Team matchup
    print("\n3. Testing Team Matchup:")
    event_id_3 = generate_admin_event_id(
        event_date="2025-06-01",
        league="NBA",
        pick_type="MoneyLine",
        team_a="Kings",
        team_b="Bulls",
        player_name=None,
        stat_threshold=None,
        stat_type=None
    )
    expected_3 = "2025-06-01-NBA-KINGS-BULLS"
    print(f"Generated: {event_id_3}")
    print(f"Expected:  {expected_3}")
    print(f"✅ Match: {event_id_3 == expected_3}")
    
    # Test 4: Player with spaces in name and stat type
    print("\n4. Testing Player with Spaces:")
    event_id_4 = generate_admin_event_id(
        event_date="2025-05-28",
        league="NBA",
        pick_type="StatOverUnder",
        team_a="Lakers",
        team_b="Warriors",
        player_name="T Haliburton",
        stat_threshold=20.5,
        stat_type="PTS"
    )
    expected_4 = "2025-05-28-NBA-THALIBURTON20.5PTS"
    print(f"Generated: {event_id_4}")
    print(f"Expected:  {expected_4}")
    print(f"✅ Match: {event_id_4 == expected_4}")
    
    # Test 5: Lowercase league
    print("\n5. Testing Lowercase League:")
    event_id_5 = generate_admin_event_id(
        event_date="2025-06-06",
        league="nba",
        pick_type="MoneyLine",
        team_a="Bucks",
        team_b="Heat",
        player_name=None,
        stat_threshold=None,
        stat_type=None
    )
    expected_5 = "2025-06-06-NBA-BUCKS-HEAT"
    print(f"Generated: {event_id_5}")
    print(f"Expected:  {expected_5}")
    print(f"✅ Match: {event_id_5 == expected_5}")
    
    print("\n" + "=" * 50)
    print("🎯 Event ID Generation Test Complete!")

if __name__ == "__main__":
    test_event_id_generation()
