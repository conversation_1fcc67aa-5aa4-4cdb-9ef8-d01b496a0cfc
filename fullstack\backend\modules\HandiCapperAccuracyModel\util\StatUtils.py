from itertools import combinations
import psutil
import os
import gc
import numpy as np
import pandas as pd


from fullstack.backend.modules.HandiCapperAccuracyModel.database.AccuracyModelDatabase import (
    AccuracyModelDatabase,
)

class StatUtils:
    def __init__(self):
        # TODO: make sure this db will be deleted later
        self.db = AccuracyModelDatabase()
        self.STAT_COLUMNS_FOR_COV = [
            "points",
            "rebounds",
            "assists",
            "personal_fouls",
            "threes_made",
            "blocks",
            "steals",
            "turnovers",
            "minutes",
            "pa",
            "pr",
            "ra",
            "pra",
            "fantasy_points",
            "off_rebounds",
            "def_rebounds",
            "fg_made",
            "fg_attempts",
            "ft_made",
            "ft_attempts",
            "threes_attempts",
        ]

    def get_stat_distribution(
        self,
        team_name,
        game_date,
        player_name,
        stat_type,
        league
    ):
        """
        Step 2-4: Build multivariate team matrix and extract the player's stat column.
        Returns the stat column name and its values (with NaNs removed).
        """
        print("league: " + league)
        players = self.db.get_current_team_members(
            team_name=team_name,
            game_date=game_date,
            league=league
        )
        cov_matrix, corr_matrix, aligned_df = self.analyze_multiplayer_stats(
            players, league
        )

        cov_matrix = cov_matrix.fillna(0)
        corr_matrix = corr_matrix.fillna(0)
        print(cov_matrix.head(5))
        print(corr_matrix.head(5))

        data_matrix = aligned_df.drop(columns=["game_date"])
        stat_column = player_name + "'s " + stat_type.lower()

        if stat_column not in data_matrix.columns:
            print("⚠️ Stat " + stat_column + " not found for " + player_name)
            return stat_column, None

        original_data = data_matrix[stat_column].values.reshape(-1, 1)
        original_data = original_data[~np.isnan(original_data)]  # Remove NaNs

        return stat_column, original_data

    def analyze_multiplayer_stats(self, players, league):
        df = self.merge_players_stats(players, league)

        # Drop non-stat columns
        stat_cols = [
            col
            for col in df.columns
            if any(stat in col.lower() for stat in self.STAT_COLUMNS_FOR_COV)
        ]
        df_stats = df[stat_cols].fillna(0)

        # Compute covariance and correlation
        cov_matrix = df_stats.cov()
        corr_matrix = df_stats.corr()

        # Top correlated and covariant stat pairs
        self.top_stat_pairs(cov_matrix, metric="Covariance")
        self.top_stat_pairs(corr_matrix, metric="Correlation")

        return cov_matrix, corr_matrix, df

    def merge_players_stats(self, players, league):
        all_dfs = []

        for player in players:
            process = psutil.Process(os.getpid())
            print(f"🔍 Memory usage (MB): {process.memory_info().rss / 1024 ** 2:.2f}")

            df = self.db.fetch_player_game_logs(player, league)
            # print("Dataframe Preview: ")
            # print(df.head(3))

            if df is None or df.empty:
                print(f"⚠️ No data for player: {player}")
                continue

            try:
                df = self.rename_player_stats(df, player, self.STAT_COLUMNS_FOR_COV)
                stat_cols = [
                    col
                    for col in df.columns
                    if any(stat in col.lower() for stat in self.STAT_COLUMNS_FOR_COV)
                ]
                df = df[["game_date"] + stat_cols]
                # print(f"✅ Columns after renaming for {player}:")
                # print(df.columns.tolist())
            except Exception as e:
                print(f"❌ Rename failed for {player}: {e}")
                continue

            all_dfs.append(df)
            del df
            gc.collect()

        if not all_dfs:
            print("❌ No player data available for merging.")
            return None

        # Concatenate all player DataFrames vertically (stacked format)
        stacked_df = pd.concat(all_dfs, ignore_index=True)
        numeric_cols = stacked_df.select_dtypes(include="number").columns.tolist()
        if "game_date" not in numeric_cols:
            numeric_cols.append("game_date")  # Ensure game_date isn't dropped

        # Pivot to wide format with player-specific stat columns
        try:
            wide_df = stacked_df.pivot_table(
                index="game_date",
                values=[col for col in numeric_cols if col != "game_date"],
                aggfunc="mean",
            ).reset_index()
        except Exception as e:
            print(f"❌ Pivot to wide format failed: {e}")
            return None

        return wide_df

    def top_stat_pairs(self, matrix, top_n=5, metric="Covariance"):
        # print_matrix_columns(matrix, label="Covariance Matrix")
        pairs = []
        for i, j in combinations(matrix.columns, 2):
            # Skip pairs from the same player
            player_i = i.split("'s")[0].strip()
            player_j = j.split("'s")[0].strip()
            if player_i == player_j:
                continue

            val = matrix.loc[i, j]
            pairs.append(((i, j), val))

        # Sort by absolute value
        sorted_pairs = sorted(pairs, key=lambda x: abs(x[1]), reverse=True)[:top_n]

        print(f"\n🔝 Top {top_n} Stat Pairs by {metric} (Different Players Only):")
        for (stat1, stat2), value in sorted_pairs:
            print(f"  {stat1} & {stat2}: {value:.2f}")

    # ✅ Rename stat columns to "Player's Stat"
    def rename_player_stats(self, df, player_name, stat_columns):
        renamed_df = df.copy()

        # Only rename the stats you actually care about
        rename_map = {
            col: f"{player_name}'s {col.replace('_', ' ')}"
            for col in stat_columns
            if col in renamed_df.columns  # only rename existing columns
        }

        return renamed_df.rename(columns=rename_map)