import React, { useState, useMemo, useEffect } from "react";
import {
  HiMagnifyingGlass,
  HiAdjustmentsHorizontal,
  HiChevronLeft,
  HiChevronRight,
} from "react-icons/hi2";
import HandicapperTable, {
  SortKey,
  SortDirection,
} from "../components/HandicapperTable";
import HandicapperModal from "../components/HandicapperModal";
import ConfirmDialog from "../components/ConfirmDialog";
import { Handicapper } from "../types/handicapper";
import useHandicappers from "../hooks/useHandicappers";

type DraftHandicapper = Omit<Handicapper, "id" | "last_updated">;
type DraftHandicapperWithPw = DraftHandicapper & { password: string };

const Homepage: React.FC = () => {
  // State Management
  const { handicappers, setHandicappers } = useHandicappers();
  const [searchTerm, setSearchTerm] = useState("");
  const [sortConfig, setSortConfig] = useState<{
    key: SortKey;
    direction: SortDirection;
  }>({ key: null, direction: null });

  const [currentPage, setCurrentPage] = useState(0);
  const ITEMS_PER_PAGE = 25;

  // Modal and Dialog State
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isConfirmOpen, setIsConfirmOpen] = useState(false);
  const [pendingHandicapper, setPendingHandicapper] =
    useState<DraftHandicapperWithPw | null>(null);

  // Filtering and Sorting Logic
  const filteredHandicappers = useMemo(
    () =>
      handicappers.filter((capper) =>
        Object.values(capper).some((value) =>
          String(value).toLowerCase().includes(searchTerm.toLowerCase())
        )
      ),
    [searchTerm, handicappers]
  );

  const sortedHandicappers = useMemo(() => {
    let sortableItems = [...filteredHandicappers];
    if (sortConfig.key && sortConfig.direction) {
      sortableItems.sort((a, b) => {
        const getVal = (capper: Handicapper): any => {
          if (sortConfig.key === "followers_total") {
            const c = capper.followers_count;
            return c.twitter + c.instagram + c.tiktok + c.youtube;
          }
          if (sortConfig.key === "last_updated") {
            return new Date(capper.last_updated).getTime();
          }
          return (capper as any)[sortConfig.key as string];
        };
        const valA = getVal(a);
        const valB = getVal(b);
        if (typeof valA === "number" && typeof valB === "number") {
          return sortConfig.direction === "ascending"
            ? valA - valB
            : valB - valA;
        }
        if (String(valA).toLowerCase() < String(valB).toLowerCase()) {
          return sortConfig.direction === "ascending" ? -1 : 1;
        }
        if (String(valA).toLowerCase() > String(valB).toLowerCase()) {
          return sortConfig.direction === "ascending" ? 1 : -1;
        }
        return 0;
      });
    }
    return sortableItems;
  }, [filteredHandicappers, sortConfig]);

  const totalPages = Math.max(
    1,
    Math.ceil(sortedHandicappers.length / ITEMS_PER_PAGE)
  );

  const paginatedHandicappers = useMemo(
    () =>
      sortedHandicappers.slice(
        currentPage * ITEMS_PER_PAGE,
        currentPage * ITEMS_PER_PAGE + ITEMS_PER_PAGE
      ),
    [sortedHandicappers, currentPage]
  );

  useEffect(() => {
    if (currentPage > totalPages - 1) {
      setCurrentPage(Math.max(0, totalPages - 1));
    }
  }, [totalPages]);

  useEffect(() => {
    setCurrentPage(0);
  }, [searchTerm]);

  // Handlers
  const handleSort = (key: SortKey) => {
    let direction: SortDirection = "ascending";
    if (sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    } else if (
      sortConfig.key === key &&
      sortConfig.direction === "descending"
    ) {
      direction = null; // Turn off sort
    }
    setSortConfig({ key, direction });
  };

  const handleModalSubmit = (newHandicapper: DraftHandicapperWithPw) => {
    setPendingHandicapper(newHandicapper);
    setIsModalOpen(false);
    setIsConfirmOpen(true);
  };

  const handleConfirmCreate = () => {
    if (!pendingHandicapper) return;

    const { password, ...capperWithoutPw } = pendingHandicapper;

    fetch("/handicappers", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ ...capperWithoutPw, password }),
    }).catch(() => {
      /* ignore errors */
    });

    const newId = Math.max(0, ...handicappers.map((h) => h.id)) + 1;
    setHandicappers([
      {
        id: newId,
        last_updated: new Date().toISOString(),
        ...capperWithoutPw,
      } as Handicapper,
      ...handicappers,
    ]);
    setPendingHandicapper(null);
    setIsConfirmOpen(false);
    setSearchTerm("");
  };

  const openCreateModal = () => {
    setPendingHandicapper({
      name: searchTerm,
      username: "",
      twitter_handle: "",
      instagram_handle: "",
      tiktok_handle: "",
      youtube_handle: "",
      profile_image_url: "",
      accuracy_score: 0,
      avg_units_per_month: 0,
      bio: "",
      pricing: "",
      group_memberships: "",
      estimated_customers: 0,
      followers_count: { twitter: 0, instagram: 0, tiktok: 0, youtube: 0 },
      password: "",
    } as any);
    setIsModalOpen(true);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (
      e.key === "Enter" &&
      searchTerm.length > 0 &&
      filteredHandicappers.length === 0
    ) {
      e.preventDefault();
      openCreateModal();
    }
  };

  const showCreateHint =
    searchTerm.length > 0 && filteredHandicappers.length === 0;

  const prevPage = () => setCurrentPage((p) => Math.max(0, p - 1));
  const nextPage = () => setCurrentPage((p) => Math.min(totalPages - 1, p + 1));

  return (
    <div className="relative min-h-screen bg-[#061844] flex flex-col items-center pt-10 pb-10">
      <div className="w-full flex justify-center mb-6 px-4">
        <img
          src="/project_parlay_logo.png"
          alt="Project Parlay Logo"
          className="h-16 w-auto select-none"
          draggable="false"
        />
      </div>

      <div className="w-full flex justify-center px-4 mt-16 mb-2">
        <div className="relative w-full max-w-xl">
          <HiMagnifyingGlass className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
          <input
            type="text"
            placeholder="Search by name, social handle, or group..."
            className="w-full bg-[#233e6c] text-white placeholder-gray-400 rounded-lg py-3.5 pl-12 pr-24 focus:outline-none focus:ring-2 focus:ring-[#58C612] text-sm shadow-md"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyDown={handleKeyDown}
          />
          <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center">
            <div className="h-5 w-px bg-gray-600 mr-3"></div>
            <button className="flex items-center gap-1.5 text-gray-300 hover:text-white transition-colors text-sm">
              <HiAdjustmentsHorizontal className="w-5 h-5" />
              <span className="hidden sm:inline">Filter</span>
            </button>
          </div>
          {showCreateHint && (
            <div
              className="absolute bottom-full mb-2 w-full bg-[#233e6c]/90 backdrop-blur-sm text-white text-center p-2.5 rounded-lg shadow-lg cursor-pointer z-20"
              onClick={openCreateModal}
            >
              Create new handicapper: "
              <span className="font-bold">{searchTerm}</span>"
            </div>
          )}
        </div>
      </div>

      <div className="flex justify-center items-center mt-8 gap-2">
        <button
          onClick={prevPage}
          disabled={currentPage === 0}
          className={`p-3 rounded-full shadow-lg transition-all duration-300 mx-2 ${
            currentPage === 0
              ? "bg-gray-700 text-gray-500 cursor-not-allowed opacity-50"
              : "bg-[#233e6c] hover:bg-[#1a2d54] text-white hover:scale-105 cursor-pointer"
          }`}
          title="Previous page"
        >
          <HiChevronLeft className="w-6 h-6" />
        </button>

        <span className="text-white text-sm">
          {currentPage + 1} / {totalPages}
        </span>

        <button
          onClick={nextPage}
          disabled={currentPage === totalPages - 1}
          className={`p-3 rounded-full shadow-lg transition-all duration-300 mx-2 ${
            currentPage === totalPages - 1
              ? "bg-gray-700 text-gray-500 cursor-not-allowed opacity-50"
              : "bg-[#233e6c] hover:bg-[#1a2d54] text-white hover:scale-105 cursor-pointer"
          }`}
          title="Next page"
        >
          <HiChevronRight className="w-6 h-6" />
        </button>
      </div>

      <HandicapperTable
        data={paginatedHandicappers}
        onSort={handleSort}
        sortConfig={sortConfig}
      />

      {isModalOpen && pendingHandicapper && (
        <HandicapperModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onSubmit={handleModalSubmit}
          initialData={pendingHandicapper}
        />
      )}

      {showCreateHint && (
        <ConfirmDialog
          isOpen={isConfirmOpen}
          onClose={() => setIsConfirmOpen(false)}
          onConfirm={handleConfirmCreate}
          title="Confirm Create Handicapper"
          message={
            pendingHandicapper
              ? `Create new handicapper named "${pendingHandicapper.name}"?`
              : ""
          }
          confirmLabel="Yes, Create"
        />
      )}
    </div>
  );
};

export default Homepage;
