export interface ExpertConfidence {
  name: string;
  confidence: number;
}

export interface ManualPick {
  name: string;
  odds: number;
  league: string[];
  pick_origin: ExpertConfidence[];
  reusable: boolean;
  capital_limit: number;
  mutual_exclusion: number;
  pick_type: "MoneyLine" | "StatOverUnder";
  player_team: string;
  stat_type: string;
  prediction: 1 | 0;
  admin_password?: string;
  player_name?: string;
  stat_threshold?: number;
  eventDate?: string; // Added for the new feature
  team_a?: string; // Team A for the event
  team_b?: string; // Team B for the event
}

export interface DatabaseEvent {
  event_id: string;
  event_date: string;
  league: string;
  team_a: string;
  team_b: string;
  actual_result?: number;
  context_features?: any;
  pick_type: string;
  player_team: string;
  stat_type: string;
  player_name?: string;
  stat_threshold?: number;
}
