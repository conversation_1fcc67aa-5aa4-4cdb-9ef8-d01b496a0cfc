import React, { createContext, useContext, useState, ReactNode } from "react";

interface HandicapperProfileContextType {
  selectedId: number | null;
  selectHandicapper: (id: number) => void;
}

const HandicapperProfileContext = createContext<
  HandicapperProfileContextType | undefined
>(undefined);

interface HandicapperProfileProviderProps {
  children: ReactNode;
}

export function HandicapperProfileProvider({
  children,
}: HandicapperProfileProviderProps) {
  const [selectedId, setSelectedId] = useState<number | null>(null);

  const selectHandicapper = (id: number) => {
    setSelectedId(id);
  };

  const value: HandicapperProfileContextType = {
    selectedId,
    selectHandicapper,
  };

  return (
    <HandicapperProfileContext.Provider value={value}>
      {children}
    </HandicapperProfileContext.Provider>
  );
}

export function useHandicapperProfile(): HandicapperProfileContextType {
  const context = useContext(HandicapperProfileContext);
  if (context === undefined) {
    throw new Error(
      "useHandicapperProfile must be used within a HandicapperProfileProvider"
    );
  }
  return context;
}
