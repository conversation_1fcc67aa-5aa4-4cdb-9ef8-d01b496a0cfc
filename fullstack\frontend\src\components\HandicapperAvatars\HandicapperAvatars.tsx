import React, { useState } from "react";
import { HiUser, HiArrowTopRightOnSquare } from "react-icons/hi2";

interface HandicapperAvatarsProps {
  handicapperNames: string[];
  maxVisible?: number;
  showLabel?: boolean;
  size?: "sm" | "md" | "lg";
  onHandicapperClick?: (handicapperName: string) => void;
}

function HandicapperAvatars({
  handicapperNames,
  maxVisible = 3,
  showLabel = true,
  size = "md",
  onHandicapperClick,
}: HandicapperAvatarsProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const getSizeClasses = () => {
    switch (size) {
      case "sm":
        return {
          avatar: "w-6 h-6 sm:w-8 sm:h-8",
          icon: "w-3 h-3 sm:w-4 sm:h-4",
          container: "w-[95%] sm:w-[104px] h-[104px]",
          title: "text-lg md:text-xl",
          text: "text-xs",
        };
      case "lg":
        return {
          avatar: "w-7 h-7 lg:w-10 lg:h-10 xl:w-12 xl:h-12",
          icon: "w-3 h-3 lg:w-5 lg:h-5 xl:w-6 xl:h-6",
          container:
            "w-[108px] lg:w-[132px] xl:w-[156px] h-[108px] lg:h-[132px] xl:h-[156px]",
          title: "text-lg lg:text-2xl xl:text-3xl",
          text: "text-sm lg:text-base",
        };
      default:
        return {
          avatar: "w-8 h-8 sm:w-10 sm:h-10",
          icon: "w-4 h-4 sm:w-6 sm:h-6",
          container: "w-[95%] sm:w-[136px] h-[136px]",
          title: "text-2xl md:text-[36px]",
          text: "text-sm",
        };
    }
  };

  const sizeClasses = getSizeClasses();
  const totalHandicappers = handicapperNames.length;
  const visibleCount = Math.min(totalHandicappers, maxVisible * 3);
  const additionalCount = Math.max(0, totalHandicappers - visibleCount);

  const handleAvatarClick = (name: string, event: React.MouseEvent) => {
    event.stopPropagation();
    if (onHandicapperClick) {
      onHandicapperClick(name);
    }
  };

  const toggleExpanded = (event: React.MouseEvent) => {
    event.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  return (
    <div className="text-center flex flex-col items-center justify-start h-full">
      {showLabel && (
        <h4
          className={`${sizeClasses.title} text-center font-bold mb-2 whitespace-nowrap`}
        >
          Picked By
        </h4>
      )}
      {!isExpanded ? (
        <>
          <div
            className={`grid grid-cols-3 gap-2 mb-2 justify-center justify-items-center content-start ${sizeClasses.container} mx-auto`}
          >
            {handicapperNames.slice(0, visibleCount).map((name, index) => (
              <Avatar
                key={index}
                name={name}
                sizeClasses={sizeClasses}
                onClick={(e) => handleAvatarClick(name, e)}
              />
            ))}
          </div>
          {additionalCount > 0 && (
            <p
              className={`${sizeClasses.text} text-gray-400 text-center mt-[-24px] sm:mt-0 hover:text-blue-400 cursor-pointer transition-colors duration-200`}
              onClick={toggleExpanded}
            >
              and {additionalCount} more
            </p>
          )}
        </>
      ) : (
        <>
          <div className={`relative ${sizeClasses.container} mx-auto`}>
            <div
              className="grid grid-cols-3 gap-2 justify-center justify-items-center content-start w-full h-full overflow-y-auto overflow-x-hidden scrollbar-thin scrollbar-track-gray-800 scrollbar-thumb-blue-500 hover:scrollbar-thumb-blue-400 handicapper-scroll"
              style={{
                scrollbarWidth: "thin",
                scrollbarGutter: "stable overlay",
                paddingRight: "0px",
              }}
            >
              {handicapperNames.map((name, index) => (
                <Avatar
                  key={index}
                  name={name}
                  sizeClasses={sizeClasses}
                  onClick={(e) => handleAvatarClick(name, e)}
                />
              ))}
            </div>
          </div>
          <p
            className={`${sizeClasses.text} text-blue-400 text-center hover:text-blue-300 cursor-pointer transition-colors duration-200`}
            onClick={toggleExpanded}
          >
            show less
          </p>
        </>
      )}
    </div>
  );
}

const Avatar = ({ name, sizeClasses, onClick }) => {
  return (
    <div
      className={`${sizeClasses.avatar} rounded-full border-2 border-gray-600 bg-gray-700 flex items-center justify-center hover:border-blue-400 cursor-pointer transition-all duration-200 relative group hover:scale-105`}
      title={`Click to view ${name}'s profile`}
      onClick={onClick}
    >
      <HiUser
        className={`${sizeClasses.icon} text-gray-400 group-hover:text-blue-300 transition-colors duration-200`}
      />
    </div>
  );
};

export default HandicapperAvatars;
