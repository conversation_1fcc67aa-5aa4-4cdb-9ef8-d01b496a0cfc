// Defined in: ai_docs/event-date-handicapper-filter/plan.md
import type { Expert } from '../types/expert';

/**
 * Fetches a list of unique experts who have predictions for a given date.
 * 
 * @param date - The target date in ISO format (YYYY-MM-DD).
 * @returns A promise that resolves to an array of Expert objects.
 */
export const getExpertsByDate = async (date: string): Promise<Expert[]> => {
  // TODO: Replace with an actual API call once the backend endpoint is available.
  // const response = await fetch(`/api/expert-predictions?date=${date}&unique=true`);
  // if (!response.ok) {
  //   throw new Error('Failed to fetch experts');
  // }
  // return response.json();

  console.warn(`API call to fetch experts for ${date} is mocked.`);
  // Return mock data for development and testing purposes.
  if (date === '2025-06-23') {
    return [
      { id: 'exp1', name: '<PERSON>' },
      { id: 'exp2', name: '<PERSON>' },
    ];
  } else if (date === '2025-06-24') {
    return [
        { id: 'exp1', name: '<PERSON>' },
        { id: 'exp3', name: '<PERSON>' },
      ];
  }

  return [];
};
