from flask import Flask, request, jsonify
from json import <PERSON><PERSON><PERSON><PERSON><PERSON>
from flask_cors import CORS
from typing import Any
import logging
import json
import os

app = Flask(__name__)
CORS(app, resources={r"/*": {"origins": "*"}})

# Ensure logger is configured
logging.basicConfig(level=logging.INFO)

# Simple password protection
ADMIN_PASSWORD = "ppadmin42"  # TODO: replace with env var in production

# Try to import database functions with fallback
try:
    from modules.HandicapperDirectoryTool import get_all_handicappers, upsert_handicapper, delete_handicapper_by_id
    DB_AVAILABLE = True
    app.logger.info("Database connection available")
except Exception as e:
    app.logger.warning(f"Database not available: {e}")
    DB_AVAILABLE = False
    
    def get_all_handicappers():
        return []
    
    def upsert_handicapper(name, data):
        app.logger.info(f"Would upsert handicapper: {name}")
        pass

    def delete_handicapper_by_id(_id):
        app.logger.info(f"Would delete handicapper id: {_id}")

# ----- API Endpoints -----

# Create a new handicapper
@app.route('/handicappers', methods=['POST'])
def create_or_update_handicapper():
    payload: dict[str, Any] = request.get_json(force=True, silent=True) or {}

    client_password = payload.pop("password", None)
    if client_password != ADMIN_PASSWORD:
        return jsonify({"status": "error", "message": "Unauthorized"}), 401

    name = payload.get("name")
    if not name or not isinstance(name, str):
        return jsonify({"status": "error", "message": "Field 'name' is required"}), 400

    try:
        upsert_handicapper(name, payload)
        status_msg = "success" if DB_AVAILABLE else "success (no database)"
        return jsonify({"status": status_msg}), 201
    except Exception as e:
        app.logger.exception("Error upserting handicapper")
        return jsonify({"status": "error", "message": str(e)}), 500

# Optional: fetch all created handicappers (debug)
@app.route('/handicappers', methods=['GET'])
def list_handicappers():
    try:
        rows = get_all_handicappers()
        for r in rows:
            fc = r.get('followers_count')
            if isinstance(fc, str):
                try:
                    r['followers_count'] = json.loads(fc)
                except Exception:
                    r['followers_count'] = {}
            ts = r.get('last_updated')
            if hasattr(ts, 'isoformat'):
                r['last_updated'] = ts.isoformat()
        return jsonify(rows)
    except Exception as e:
        app.logger.exception("Failed to fetch handicappers")
        return jsonify([]), 200

@app.route('/health')
def health_check():
    return jsonify({
        "status": "healthy",
        "database": "available" if DB_AVAILABLE else "unavailable",
        "port": os.environ.get("PORT", "5000")
    })

@app.route('/')
def index():
    return jsonify({"Status": "Server is running", "database": DB_AVAILABLE})

@app.route('/handicappers/<int:handicapper_id>', methods=['DELETE'])
def delete_handicapper_endpoint(handicapper_id):
    payload = request.get_json(force=True, silent=True) or {}
    client_password = payload.pop("password", None)
    if client_password != ADMIN_PASSWORD:
        return jsonify({"status": "error", "message": "Unauthorized"}), 401
    try:
        delete_handicapper_by_id(handicapper_id)
        status_msg = "success" if DB_AVAILABLE else "success (no database)"
        return jsonify({"status": status_msg}), 200
    except Exception as e:
        app.logger.exception("Error deleting handicapper")
        return jsonify({"status": "error", "message": str(e)}), 500

if __name__ == '__main__':
    port = int(os.environ.get("PORT", 5000))
    app.run(host='0.0.0.0', port=port, debug=False)
