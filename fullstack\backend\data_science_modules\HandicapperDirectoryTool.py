from planet_scale_port import get_dict_connection
import json
import pandas as pd

def normalize_string(value):
    if isinstance(value, str):
        return value.strip().lower().replace(" ", "_")
    return value


def insert_handicapper(data):
    conn = get_dict_connection()
    try:
        with conn.cursor() as cursor:
            sql = """
                INSERT INTO handicappers (
                    name, username, twitter_handle, instagram_handle, tiktok_handle, youtube_handle,
                    profile_image_url, accuracy_score, avg_units_per_month, bio, pricing,
                    group_memberships, estimated_customers, followers_count
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            cursor.execute(sql, (
                normalize_string(data.get("name")),
                normalize_string(data.get("username")),
                normalize_string(data.get("twitter_handle")),
                normalize_string(data.get("instagram_handle")),
                normalize_string(data.get("tiktok_handle")),
                normalize_string(data.get("youtube_handle")),
                data.get("profile_image_url"),
                data.get("accuracy_score"),
                data.get("avg_units_per_month"),
                data.get("bio"),
                data.get("pricing"),
                data.get("group_memberships"),
                data.get("estimated_customers"),
                json.dumps(data.get("followers_count"))
            ))
        conn.commit()
    finally:
        conn.close()

def update_handicapper(handicapper_id, data):
    conn = get_dict_connection()
    try:
        with conn.cursor() as cursor:
            sql = """
                UPDATE handicappers SET
                    name=%s, username=%s, twitter_handle=%s, instagram_handle=%s,
                    tiktok_handle=%s, youtube_handle=%s, profile_image_url=%s, accuracy_score=%s,
                    avg_units_per_month=%s, bio=%s, pricing=%s, group_memberships=%s,
                    estimated_customers=%s, followers_count=%s, last_updated=CURRENT_TIMESTAMP
                WHERE id=%s
            """
            cursor.execute(sql, (
                normalize_string(data.get("name")),
                normalize_string(data.get("username")),
                normalize_string(data.get("twitter_handle")),
                normalize_string(data.get("instagram_handle")),
                normalize_string(data.get("tiktok_handle")),
                normalize_string(data.get("youtube_handle")),
                data.get("profile_image_url"),
                data.get("accuracy_score"),
                data.get("avg_units_per_month"),
                data.get("bio"),
                data.get("pricing"),
                data.get("group_memberships"),
                data.get("estimated_customers"),
                json.dumps(data.get("followers_count")),
                handicapper_id
            ))
        conn.commit()
    finally:
        conn.close()

def search_handicapper(name_to_search):
    conn = get_dict_connection()
    try:
        with conn.cursor() as cursor:
            normalized = normalize_string(name_to_search)
            like_query = f"%{normalized}%"
            sql = """
                SELECT * FROM handicappers
                WHERE LOWER(REPLACE(name, ' ', '_')) LIKE %s
                   OR LOWER(REPLACE(instagram_handle, ' ', '_')) LIKE %s
                LIMIT 1
            """
            cursor.execute(sql, (like_query, like_query))
            result = cursor.fetchone()
            return result
    finally:
        conn.close()

def get_all_handicappers():
    conn = get_dict_connection()
    try:
        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT *
                FROM handicappers
                ORDER BY name ASC
            """)
            return cursor.fetchall()
    finally:
        conn.close()

def upsert_handicapper(name_to_search, data):
    existing = search_handicapper(name_to_search)
    if existing:
        print(f"Found existing record for '{existing['name']}' (id={existing['id']}), updating...")
        update_handicapper(existing["id"], data)
    else:
        print(f"No match found for '{name_to_search}', inserting new handicapper...")
        insert_handicapper(data)

