from fullstack.backend.data_science_modules.planet_scale_port import (
    get_connection,
    get_dict_connection,
    get_player_and_stat_type,
)
import pandas as pd
import pymysql
from typing import Dict, List, Optional, Tuple


class AccuracyModelDatabase:
    def __init__(self):
        self._conn = get_connection()
        self._dict_conn = get_dict_connection()
        self._cursor = self._conn.cursor()

    def close(self):
        if self._conn:
            self._conn.close()
            self._dict_conn.close()
            self._conn = None
            self._dict_conn = None

    def fetch_all_events(self):
        self._cursor.execute(
            """
            SELECT e.event_id, c.crowd_probability, e.actual_result
            FROM events e
            JOIN crowd_predictions c ON e.event_id = c.event_id
            WHERE e.actual_result IS NOT NULL
        """
        )
        events = self._cursor.fetchall()

        event_data = []
        for event_id, crowd_prob, actual in events:
            self._cursor.execute(
                """
                SELECT expert_name, prediction, confidence, stat_threshold
                FROM expert_predictions
                WHERE event_id = %s
            """,
                (event_id,),
            )
            experts = self._cursor.fetchall()
            event_data.append(
                {
                    "event_id": event_id,
                    "crowd_prob": crowd_prob,
                    "expert_predictions": experts,
                    "actual_result": actual,
                }
            )
        return event_data

    def get_expert_reliability(self):
        self._cursor.execute(
            """
            SELECT expert_name, 
                   true_positive, false_positive,
                   true_negative, false_negative
            FROM expert_reliability
        """
        )
        rows = self._cursor.fetchall()

        reliability = {}
        for name, tp, fp, tn, fn in rows:
            alpha = tp / (tp + fn) if (tp + fn) > 0 else 0.5  # P("Higher" | Y=1)
            beta = tn / (tn + fp) if (tn + fp) > 0 else 0.5  # P("Lower" | Y=0)
            reliability[name] = (alpha, beta)
        return reliability

    def fetch_expert_accuracy(self, expert_name):
        self._cursor.execute(
            """
            SELECT correct_predictions, total_predictions
            FROM expert_reliability
            WHERE expert_name = %s
            """,
            (expert_name,),
        )
        row = self._cursor.fetchone()
        return row

    def fetch_expert_predictions_by_event(self) -> pd.DataFrame:
        query = """
                SELECT event_id, expert_name, prediction
                FROM expert_predictions \
                """
        df = pd.read_sql_query(query, self._conn)
        return df

    def fetch_crowd_probability(self, event_id):
        """
        Retrieve the crowd probability (prior P(Y=1)) for a given event_id.
        """
        self._cursor.execute(
            "SELECT crowd_probability FROM crowd_predictions WHERE event_id = %s",
            (event_id,),
        )
        row = self._cursor.fetchone()
        if not row:
            print(f"❌ Event ID '{event_id}' not found or missing crowd probability.")
            return None
        return row[0]

    def fetch_expert_predictions(self, event_id):
        """
        Fetch expert predictions for a given event_id.
        Each prediction includes: expert_name, prediction (0/1), and confidence (float).
        """
        self._cursor.execute(
            "SELECT expert_name, prediction, confidence FROM expert_predictions WHERE event_id = %s",
            (event_id,),
        )
        predictions = self._cursor.fetchall()
        if not predictions:
            print(f"⚠️ No expert predictions found for event '{event_id}'.")
        return predictions

    def fetch_event_metadata(self, event_id: str) -> Optional[Dict]:
        """
        Retrieves metadata for a given event ID from the events table.

        Returns a dictionary with:
            - player_name
            - league
            - game_date (as string YYYY-MM-DD)
            - stat_type
            - stat_threshold

        Returns None if the event is not found.
        """

        query = """
                SELECT player_name, league, event_date, stat_type, stat_threshold
                FROM events
                WHERE event_id = %s \
                """
        self._cursor.execute(query, (event_id,))
        row = self._cursor.fetchone()

        if not row:
            print(f"❌ No metadata found for event_id: {event_id}")
            return None

        return {
            "player_name": row[0],
            "league": row[1],
            "game_date": str(row[2]),
            "stat_type": row[3],
            "stat_threshold": row[4],
        }

    def get_team_name(self, player_name, target_date, league):
        """
        Step 1: 🔍 Look up the player's team on the target date.
        Returns the team abbreviation (e.g., 'MIA' for Miami Heat).
        """
        team_query = f"""
            SELECT * FROM {league}_Player_Logs
            WHERE player_name = %s AND game_date = %s
            LIMIT 10
        """
        cursor = self._dict_conn.cursor()
        cursor.execute(team_query, (player_name, target_date))
        rows = cursor.fetchall()

        for row in rows:
            dict_row = dict(row)
            if "team" in dict_row:
                return dict_row["team"]

        print(f"❌ No team info found for {player_name} on {target_date}")
        return None

    # Returns all current active team members names that will play on the target date, using MySQL database, for the target team and league
    def get_current_team_members(self, team_name: str, game_date: str, league: str):
        conn = get_connection()
        cursor = conn.cursor(pymysql.cursors.DictCursor)

        print("team: " + team_name)
        print("Date: " + game_date)
        print("leauge: " + league)

        cursor.execute(
            """
            SELECT DISTINCT player_name
            FROM NBA_Player_Logs
            WHERE team = %s
              AND game_date = %s
              AND league = %s
            """,
            (team_name, game_date, league),
        )

        rows = cursor.fetchall()
        conn.close()

        team_members = [row["player_name"] for row in rows]
        print("Teammate Names: " + str(team_members))

        return team_members

    def fetch_player_game_logs(self, player_name: str, league: str) -> pd.DataFrame:
        """
        Fetches all available game logs for a given player from the appropriate MySQL league table.
        """
        df = pd.DataFrame()

        table = f"""
            SELECT * FROM {league}_Player_Logs
            WHERE player_name = %s
            ORDER BY game_date ASC
        """
        cursor = self._dict_conn.cursor()
        cursor.execute(table, (player_name,))
        rows = cursor.fetchall()

        # Selects Rows
        for i, row in enumerate(rows):
            new_row = dict(row)
            new_row_df = pd.DataFrame([new_row])
            df = pd.concat([df, new_row_df], ignore_index=True)

        return df

    def fetch_expert_predictions_for_team_date(
        self, team: str, league: str, game_date: str
    ) -> pd.DataFrame:
        """
        Fetches all expert predictions for players on the given team, in the given league, for a specific game date.

        Parameters:
            team (str): The team abbreviation (e.g., "MIA").
            league (str): The league (e.g., "NBA").
            game_date (str): The game date in 'YYYY-MM-DD' format.
            connection: MySQL database connection object.

        Returns:
            pd.DataFrame: A DataFrame containing all matching expert predictions.
        """
        query = """
                SELECT event_id, \
                       stat_threshold, \
                       confidence, \
                       prediction, \
                       expert_name
                FROM expert_predictions
                WHERE team = %s
                  AND league = %s
                  AND game_date = %s \
                """
        try:
            cursor = self._dict_conn.cursor()
            cursor.execute(query, (team, league, game_date))
            rows = cursor.fetchall()

            df2 = pd.DataFrame(
                columns=[
                    "event_id",
                    "stat_threshold",
                    "confidence",
                    "prediction",
                    "expert_name",
                ]
            )

            # Selects First Row
            for i, row in enumerate(rows):
                df2.loc[i] = row

            print("expert predictions for teamates df 2:")
            print(df2.head(5))

            df2["player_name"] = [None] * len(rows)
            df2["stat_type"] = [None] * len(rows)

            count = 0
            for event_id in df2["event_id"]:
                print(event_id)

                result = get_player_and_stat_type(event_id)

                if result:
                    print(f"Player Name: {result[0]}, Stat Type: {result[1]}")
                    df2.loc[count, "player_name"] = result[0]
                    df2.loc[count, "stat_type"] = result[1]

                else:
                    print("No data found for the given event_id.")

                count += 1

            print("expert predictions for teamates df 3:")
            print(df2.head(5))
            return df2

        except Exception as e:
            print(f"❌ Error fetching expert predictions: {e}")
            return pd.DataFrame()
