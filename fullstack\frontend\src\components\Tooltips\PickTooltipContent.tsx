interface PickTooltipContentProps {
  pick: {
    pID: string | number;
    odds: string;
    confidence?: number;
    name?: string;
    bayesian_conf?: number;
    bayesian_prob?: number;
    logistic_prob?: number;
    capital_limit?: number;
    event_id?: string;
    gameID?: number;
    league?: string;
    reusable?: boolean;
    stat_type?: string;
  };
}

export default function PickTooltipContent({ pick }: PickTooltipContentProps) {
  return (
    <div className="px-4 py-3 bg-gray-900 text-white text-sm rounded-lg shadow-xl min-w-[280px]">
      <div className="space-y-2">
        <div className="border-b border-gray-600 pb-2">
          <div className="font-bold text-green-400">
            {pick.name || `Pick #${pick.pID}`}
          </div>
          <div className="text-gray-300 text-xs">
            {pick.league} • {pick.stat_type}
          </div>
        </div>
        <div className="grid grid-cols-2 gap-3">
          <div>
            <div className="text-white text-xs">Confidence</div>
            <div className="font-semibold text-white">
              {pick.confidence?.toFixed(0)}%
            </div>
          </div>
          <div>
            <div className="text-white text-xs">Odds</div>
            <div className="font-semibold text-white">{pick.odds}</div>
          </div>
        </div>
        <div className="border-t border-gray-600 pt-2">
          <div className="text-white text-xs mb-1">Probability Analysis</div>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div>
              <span className="text-white">Bayesian:</span> <span className="text-white">{pick.bayesian_prob ? (pick.bayesian_prob * 100).toFixed(1) : "N/A"}%</span>
            </div>
            <div>
              <span className="text-white">Logistic:</span> <span className="text-white">{pick.logistic_prob ? (pick.logistic_prob * 100).toFixed(1) : "N/A"}%</span>
            </div>
          </div>
        </div>
        <div className="border-t border-gray-600 pt-2 text-xs space-y-1">
          <div className="text-white">
            Event ID: <span className="text-white">{pick.event_id || "N/A"}</span>
          </div>
          <div className="text-white">
            Bayesian Conf: <span className="text-white">{pick.bayesian_conf?.toFixed(2) || "N/A"}</span>
          </div>
          {pick.gameID && pick.gameID !== -1 && (
            <div className="text-white">
              Game ID: <span className="text-white">{pick.gameID}</span>
            </div>
          )}
          {pick.capital_limit !== undefined && (
            <div className="text-white">
              Capital Limit: <span className="text-white">{pick.capital_limit}</span>
            </div>
          )}
          {pick.reusable !== undefined && (
            <div className="text-white">
              Reusable: <span className="text-white">{pick.reusable ? "Yes" : "No"}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 