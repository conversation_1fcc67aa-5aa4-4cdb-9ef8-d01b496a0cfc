// Defined in: ai_docs/event-date-handicapper-filter/plan.md
import React from 'react';
import styles from './DateSelector.module.css';

interface DateSelectorProps {
  value: string; // Expects date in YYYY-MM-DD format
  onChange: (date: string) => void;
  label?: string;
}

const DateSelector: React.FC<DateSelectorProps> = ({ value, onChange, label = 'Event Date' }) => {
  return (
    <div className={styles.dateSelectorContainer}>
      {label && <label htmlFor="event-date-selector">{label}</label>}
      <input
        id="event-date-selector"
        type="date"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className={styles.dateInput}
      />
    </div>
  );
};

export default DateSelector;
