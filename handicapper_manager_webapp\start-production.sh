#!/bin/bash

echo "Starting production servers..."

# Kill any existing processes
pkill -f "python.*app.py" || true
pkill -f "node.*server.js" || true
sleep 2

# Start frontend first so Replit binds to its port
echo "Starting frontend server first..."
npm run serve:frontend &
FRONTEND_PID=$!

# Give frontend a moment to start
sleep 5

# Start backend
echo "Starting Flask backend..."
cd handicapper_manager_backend
PORT=5000 python app.py &
BACKEND_PID=$!
cd ..

# Monitor both processes
wait 