
export const getConfidenceColor = (score?: number): string => {
  if (score === undefined || score === null) return "rgb(128, 128, 128)";
  const s = Math.max(0, Math.min(score, 100));
  const t = Math.pow(s / 100, 2.9);
  const r = Math.round((1 - t) * 210 + t * 0);
  const g = Math.round((1 - t) * 180 + t * 200);
  const b = Math.round((1 - t) * 140 + t * 0);
  return `rgb(${r}, ${g}, ${b})`;
};

export const getConfidenceColorRGB = (
  score?: number
): { r: number; g: number; b: number } => {
  if (score === undefined || score === null) return { r: 128, g: 128, b: 128 };
  const s = Math.max(0, Math.min(score, 100));
  const t = Math.pow(s / 100, 2.9);
  const r = Math.round((1 - t) * 210 + t * 0);
  const g = Math.round((1 - t) * 180 + t * 200);
  const b = Math.round((1 - t) * 140 + t * 0);
  return { r, g, b };
};

