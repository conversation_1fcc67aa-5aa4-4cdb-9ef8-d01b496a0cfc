/* Defined in: ai_docs/event-date-handicapper-filter/plan.md */

.dateSelectorContainer {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.dateInput {
    padding: 0.75rem;
    border: 1px solid #4A5568; /* gray-700 */
    border-radius: 0.375rem; /* rounded-md */
    font-size: 1rem;
    color: #E2E8F0; /* gray-200 */
    background-color: #233e6c;
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.dateInput:hover {
    border-color: #58C612;
    cursor: pointer;
}

.dateInput:focus, .dateInput:focus-visible {
    outline: none;
    border-color: #58C612;
    box-shadow: 0 0 0 2px rgba(88, 198, 18, 0.5);
}

.dateInput::-webkit-calendar-picker-indicator {
    cursor: pointer;
    filter: invert(1) brightness(0.8);
    transition: filter 0.2s ease-in-out;
}

.dateInput::-webkit-calendar-picker-indicator:hover {
    filter: invert(1) brightness(1);
}
