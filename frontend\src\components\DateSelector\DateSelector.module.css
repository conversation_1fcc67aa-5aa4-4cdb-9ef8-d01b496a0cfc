/* Defined in: ai_docs/event-date-handicapper-filter/plan.md */

.dateSelectorContainer {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.dateInput {
    padding: 0.75rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1rem;
    color: #333;
    background-color: #fff;
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.dateInput:hover {
    border-color: #888;
    cursor: pointer;
}

.dateInput:focus, .dateInput:focus-visible {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Style for the calendar picker indicator */
.dateInput::-webkit-calendar-picker-indicator {
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.2s ease-in-out;
}

.dateInput::-webkit-calendar-picker-indicator:hover {
    opacity: 1;
}
