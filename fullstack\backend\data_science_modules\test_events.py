""" add_event """

# from planet_scale_port import add_event, get_connection

# def test_add_event():
#     # Test 1: Normal insert with actual_result = 1
#     event_id = "2025-06-01-NBA-KINGS-BULLS"
#     add_event(event_id, "2025-06-01", "NBA", "Kings", "Bulls", 1)

#     with get_connection() as conn:
#         cursor = conn.cursor()
#         cursor.execute("SELECT actual_result FROM events WHERE event_id = %s", (event_id,))
#         result = cursor.fetchone()[0]
#         print(f"{event_id} actual_result:", result)  # Should print 1

#     # Test 2: Insert with actual_result = None (explicit NULL)
#     event_id_null = "2025-06-02-NBA-KINGS-BULLS"
#     add_event(event_id_null, "2025-06-02", "NBA", "Kings", "Bulls", None)

#     with get_connection() as conn:
#         cursor = conn.cursor()
#         cursor.execute("SELECT actual_result FROM events WHERE event_id = %s", (event_id_null,))
#         result = cursor.fetchone()[0]
#         print(f"{event_id_null} actual_result:", result)  # Should print None

#     # Test 3: Insert with no actual_result argument (uses default = None)
#     event_id_default_null = "2025-06-03-NBA-KINGS-BULLS"
#     add_event(event_id_default_null, "2025-06-03", "NBA", "Kings", "Bulls")

#     with get_connection() as conn:
#         cursor = conn.cursor()
#         cursor.execute("SELECT actual_result FROM events WHERE event_id = %s", (event_id_default_null,))
#         result = cursor.fetchone()[0]
#         print(f"{event_id_default_null} actual_result:", result)  # Should also print None

# test_add_event()

""" insert_nba_player_log """

# from planet_scale_port import insert_nba_player_log, get_connection

# def test_insert_nba_player_log():
#     
#     insert_nba_player_log(
#         player_name="Demar Derozan",
#         game_date="2025-06-08",
#         team="Kings",
#         opponent="Warriors",
#         points=28,
#         rebounds=5,
#         assists=7,
#         personal_fouls=2,
#         threes_made=1,
#         blocks=None,
#         steals=1,
#         turnovers=2,
#         minutes=36.5
#     )

#     Verify insertion in database
#     with get_connection() as conn:
#         cursor = conn.cursor()
#         cursor.execute("""
#             SELECT player_name, game_date, points, assists, blocks 
#             FROM NBA_Player_Logs 
#             WHERE player_name = %s AND game_date = %s
#         """, ("Demar Derozan", "2025-06-08"))
#         row = cursor.fetchone()
#         print("Inserted Log:", row)

# test_insert_nba_player_log()

""" update_nba_derived_fields """

# from planet_scale_port import insert_nba_player_log, update_nba_derived_fields, get_connection

# def test_update_nba_derived_fields():
#     # Insert a new row into NBA_Player_Logs
#     insert_nba_player_log(
#         player_name="Domantas Sabonis",
#         game_date="2025-06-09",
#         team="Kings",
#         opponent="Spurs",
#         points=20,
#         rebounds=12,
#         assists=8,
#         blocks=1,
#         steals=2,
#         turnovers=4,
#         minutes=34
#     )

#     # Run the derived fields update function
#     update_nba_derived_fields()

    
#     with get_connection() as conn:
#         cursor = conn.cursor()
#         cursor.execute("""
#     SELECT entry_id, points, rebounds, assists, blocks, steals, turnovers
#     FROM NBA_Player_Logs
#     WHERE game_date >= %s
# """, ("2025-06-09",))
        
        
#         row = cursor.fetchone()

#         if row:
#             pa, pr, ra, pra, fantasy_points = row
#             print(f"PA: {pa}, PR: {pr}, RA: {ra}, PRA: {pra}, Fantasy Points: {fantasy_points:.1f}")
#         else:
#             print("No record found for Domantas Sabonis on 2025-06-09.")

# test_update_nba_derived_fields()


""" add_crowd_prediction """

# from planet_scale_port import add_crowd_prediction
# from planet_scale_port import get_connection

# 
# add_crowd_prediction("2025-05-25-NBA-SUNS-KNICKS", 0.68)

#
# with get_connection() as conn:
#     cursor = conn.cursor()
#     cursor.execute("""
#         SELECT crowd_probability, source_name 
#         FROM crowd_predictions
#         WHERE event_id = %s
#     """, ("2025-05-25-NBA-SUNS-KNICKS",))
#     row = cursor.fetchone()
#     print("After Insert:", row)

# # Update the prediction
# add_crowd_prediction("2025-05-25-NBA-SUNS-KNICKS", 0.75)

# 
# with get_connection() as conn:
#     cursor = conn.cursor()
#     cursor.execute("""
#         SELECT crowd_probability 
#         FROM crowd_predictions
#         WHERE event_id = %s
#     """, ("2025-05-25-NBA-SUNS-KNICKS",))
#     row = cursor.fetchone()
#     print("After Update:", row)


""" add_expert_prediction """

# from planet_scale_port import add_expert_prediction, get_connection

# def test_add_expert_prediction():
#     event_id = "2025-06-04-NBA-WARRIORS-CELTICS"

#     
#     add_expert_prediction(event_id, "Steph Curry", 1, 0.85)
#     with get_connection() as conn:
#         cursor = conn.cursor()
#         cursor.execute("""
#             SELECT prediction, confidence FROM expert_predictions
#             WHERE event_id = %s AND expert_name = %s
#         """, (event_id, "Steph Curry"))
#         print("With Confidence:", cursor.fetchone())

#     # Insert without confidence (None)
#     add_expert_prediction(event_id, "Davante Adams", 0)  # confidence is None by default
#     with get_connection() as conn:
#         cursor = conn.cursor()
#         cursor.execute("""
#             SELECT prediction, confidence FROM expert_predictions
#             WHERE event_id = %s AND expert_name = %s
#         """, (event_id, "Davante Adams"))
#         print("Without Confidence:", cursor.fetchone())

# test_add_expert_prediction()

""" add_multiple_expert_predictions """
# from planet_scale_port import add_multiple_expert_predictions, get_connection

# def test_add_multiple_expert_predictions():
#     event_id = "2025-06-05-NBA-NETS-SPURS"

#     predictions = [
#         (event_id, "Analyst1", 1, 0.92),
#         (event_id, "Analyst2", 0, 0.75),
#         (event_id, "Analyst3", 1, None)  # Test with NULL confidence
#     ]

#     
#     add_multiple_expert_predictions(predictions)

#     
#     with get_connection() as conn:
#         cursor = conn.cursor()
#         cursor.execute("""
#             SELECT expert_name, prediction, confidence 
#             FROM expert_predictions
#             WHERE event_id = %s
#             ORDER BY expert_name
#         """, (event_id,))
#         results = cursor.fetchall()
#         print("Inserted Predictions:")
#         for row in results:
#             print(row)

# test_add_multiple_expert_predictions()


""" generate_event """

# from planet_scale_port import generate_event_id, get_connection, add_event
# from datetime import datetime

# def test_generate_event_id_with_sql_check():
#     today = datetime.today().strftime('%Y-%m-%d')
#     pick_name = "Stephen Curry/3.5 3PM"
#     league = "NBA"

#     
#     event_id = generate_event_id(pick_name, league)
#     expected_id = f"{today}-NBA-STEPHENCURRY-3.53PM"
#     print("Generated ID:", event_id)
#     print("Expected ID:", expected_id)

#    
#     add_event(event_id, today, league, "Over", "Under")

#     
#     with get_connection() as conn:
#         cursor = conn.cursor()
#         cursor.execute("SELECT event_id FROM events WHERE event_id = %s", (event_id,))
#         result = cursor.fetchone()
#         print("SQL Check - Event ID Found in DB:", result[0] if result else "None")

# test_generate_event_id_with_sql_check()


""" delete_event """

# from planet_scale_port import delete_event, get_connection

# def test_delete_event():
#     event_id = "2025-06-04-NBA-WARRIORS-CELTICS"

#     
#     delete_event(event_id)

#     
#     with get_connection() as conn:
#         cursor = conn.cursor()
#         cursor.execute("SELECT * FROM events WHERE event_id = %s", (event_id,))
#         print("Event exists after delete:", cursor.fetchone())

#         
#         cursor.execute("SELECT * FROM expert_predictions WHERE event_id = %s", (event_id,))
#         print("Expert prediction exists after delete:", cursor.fetchone())

#         
#         cursor.execute("SELECT * FROM crowd_predictions WHERE event_id = %s", (event_id,))
#         print("Crowd prediction exists after delete:", cursor.fetchone())

# test_delete_event()

""" fetch_events_by_date """

# from planet_scale_port import fetch_events_by_date

# # Test for a known date
# fetch_events_by_date("2025-01-06")

# # Test for a date with no events
# fetch_events_by_date("2099-01-01")

# # Test for NULL result
# fetch_events_by_date("2025-05-14")

""" fetch_predictions_by_expert """
# from planet_scale_port import add_event, add_expert_prediction, fetch_predictions_by_expert
# from datetime import datetime

# def test_fetch_predictions_by_expert():
#     
#     event_id_1 = "2025-06-06-NBA-BUCKS-HEAT"
#     event_id_2 = "2025-06-07-NBA-BUCKS-HEAT"

#     add_event(event_id_1, "2025-06-06", "NBA", "Bucks", "Heat")
#     add_event(event_id_2, "2025-06-07", "NBA", "Bucks", "Heat")

#     add_expert_prediction(event_id_1, "Expert1", 1, 0.85)
#     add_expert_prediction(event_id_2, "Expert2", 0, None)

#     
#     fetch_predictions_by_expert("Expert1")

# test_fetch_predictions_by_expert()

# from datetime import datetime
# from goalserve_test import fetch_MLB_team_stats, insert_mlb_player_log
# from planet_scale_port import bulk_import_mlb_logs

# bulk_import_mlb_logs(
#     api_key=b6cccb2cee43489fcb7908dd8718f057,
#     start_date=datetime(2024, 3, 28),
#     end_date=datetime.today(),
#     fetch_fn=fetch_MLB_team_stats
# )

""" bulk_import_mlb_logs"""

from datetime import datetime
from goalserve_test import fetch_MLB_team_stats
from planet_scale_port import bulk_import_mlb_logs, bulk_insert_mlb_logs
bulk_import_mlb_logs("b6cccb2cee43489fcb7908dd8718f057", datetime(2024, 10, 5), datetime(2024, 10, 30))

""" fetch_mlb_team_stats """
# statTest = fetch_MLB_team_stats("b6cccb2cee43489fcb7908dd8718f057", "18.05.2025", "Dodgers")
# print(f"Fetched {len(statTest)} stats for Dodgers on 18.05.2025")
# for stat in statTest:
#     print(stat)

