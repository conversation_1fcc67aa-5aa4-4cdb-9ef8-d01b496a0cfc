from typing import Dict, List, Optional, Tuple
import numpy as np
from fullstack.backend.modules.HandiCapperAccuracyModel.database.AccuracyModelDatabase import (
    AccuracyModelDatabase,
)
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import GridSearchCV
from sklearn.neighbors import KernelDensity
from random import choices
from scipy.special import expit  # sigmoid
import math


class BayesianLogisticPredictor:
    def __init__(self):
        self.db = AccuracyModelDatabase()

    def close(self):
        self.db.close()

    def run_prediction(self, event_id, logistic_model, logistic_expert_names):
        print("🔮 Generating ensemble prediction...")
        result = self._predict_event_by_id(
            event_id, logistic_model, logistic_expert_names
        )
        if result is None:
            print("❌ Prediction failed.")
            return {"error": "Prediction failed. Check logs for more info."}
        return result

    def _predict_event_by_id(
        self, event_id, logistic_model=None, logistic_expert_names=None, n_samples=1000
    ):
        """
        Computes a Bayesian ensemble prediction for a single event using expert signals and crowd probability.

        This function:
        - Retrieves the crowd probability (prior) and expert predictions for the given event_id from the database.
        - Applies a Bayesian update rule, treating expert signals as evidence to adjust the crowd's prior probability.
        - Incorporates per-expert reliability (true positive rate, true negative rate) from the database.
        - Enhances accuracy by using expert-specific confidence calibration (how well experts perform at different confidence levels).
        - Includes support for down-weighting correlated sources to avoid double counting overlapping information.
        - Uses Bayesian simulation (Monte Carlo sampling) to estimate a posterior distribution of the event probability.
        - Returns a point estimate (mean posterior probability) along with a 95% credible interval.
        - Predicts binary outcome class (1 = likely to happen, 0 = likely not).
        """
        # === Load Expert Reliability + Correlation ===
        # TODO: fetching data could be done in main model
        reliability = self.db.get_expert_reliability()
        correlation_matrix = self.compute_expert_correlation_matrix()

        # === Get Crowd Probability ===
        # TODO: fetching data is still unrelated could be changed to main model
        crowd_prob = self.db.fetch_crowd_probability(event_id)
        if crowd_prob is None:
            return None

        # === Get Expert Predictions for the Event ===
        # TOOD: fetching data could be done in main model
        expert_predictions = self.db.fetch_expert_predictions(event_id)
        if not expert_predictions:
            return None

        # === Logistic Regression Prediction ===
        logistic_prob = self.predict_logistic_probability(
            expert_predictions, logistic_model, logistic_expert_names
        )

        # --- Bootstrapping to estimate uncertainty ---
        mean_prob, std_dev = self.estimate_bayesian_probability_with_uncertainty(
            crowd_prob, expert_predictions, reliability, correlation_matrix, n_samples
        )

        # --- Asessing Quality Of Prediction ---
        quality_score = self.compute_prediction_quality(
            crowd_prob,
            expert_predictions,
            {k: self.get_expert_accuracy(k) for k, _, _ in expert_predictions},
        )

        # === Combine using quality score as weight ===
        combined_prob = self.combine_predictions(
            mean_prob, logistic_prob, quality_score
        )
        combined_class = 1 if combined_prob >= 0.5 else 0

        # === Display Final Breakdown ===
        # TODO: This could be done outside, with utils
        # After deleting all the db fetch, event_id would not need to be passed in
        self.log_prediction_output(
            event_id,
            mean_prob,
            std_dev,
            quality_score,
            logistic_prob,
            combined_prob,
            combined_class,
        )

        return {
            "event_id": event_id,
            "bayesian_prob": mean_prob,
            "logistic_prob": logistic_prob,
            "combined_prob": combined_prob,
            "error_margin": std_dev,
            "quality_score": quality_score,
            "predicted_class": combined_class,
        }

    def compute_expert_correlation_matrix(self):
        df = self.db.fetch_expert_predictions_by_event()

        # Pivot to event_id as rows, experts as columns
        pivot_df = df.pivot_table(
            index="event_id", columns="expert_name", values="prediction"
        )
        pivot_df = pivot_df.dropna(
            thresh=2
        )  # Drop rows with fewer than 2 expert predictions

        # Compute pairwise correlation (NaN-safe)
        correlation_matrix = pivot_df.corr(method="pearson", min_periods=5).fillna(0)

        return correlation_matrix

    def predict_logistic_probability(self, expert_predictions, model, expert_names):
        """
        Compute logistic model prediction for an event based on expert voting.
        Converts expert predictions into a vector of -1/1 (or 0 if absent) and uses logistic_model.predict_proba().
        """
        vec = np.zeros(len(expert_names))
        name_to_idx = {name: i for i, name in enumerate(expert_names)}
        for name, pred, _ in expert_predictions:
            if name in name_to_idx:
                vec[name_to_idx[name]] = 1 if pred == 1 else -1
        return round(model.predict_proba([vec])[0][1], 4)

    def estimate_bayesian_probability_with_uncertainty(
        self, crowd_prob, expert_predictions, reliability, correlation_matrix, n_samples
    ):
        """
        Estimate Bayesian probability using bootstrapping.
        Repeatedly sample expert predictions and compute Bayesian posterior for each sample.
        Returns: (mean posterior probability, standard deviation)
        """
        probs = []
        for _ in range(n_samples):
            sampled = choices(expert_predictions, k=len(expert_predictions))
            prob = self.compute_bayesian_posterior(
                crowd_prob, sampled, reliability, correlation_matrix=correlation_matrix
            )
            probs.append(prob)
        return round(np.mean(probs), 4), round(np.std(probs), 4)

    def compute_bayesian_posterior(
        self, crowd_prob, expert_predictions, reliability, correlation_matrix
    ):
        """
        Computes the posterior probability P(Y=1) using:
        - Crowd probability as prior
        - Expert predictions as evidence (with confidence)
        - Expert reliability (TPR, TNR)
        - Correlation matrix to down-weight redundant sources
        """
        logit = self.log_odds(crowd_prob)

        # Compute correlation-based weight scaling
        correlation_weights = self.adjust_weights_for_correlation(
            expert_predictions, correlation_matrix
        )

        for name, signal, conf in expert_predictions:
            conf = conf if conf is not None else 0.75

            alpha, beta = reliability.get(name, (0.5, 0.5))
            weight = 0

            if signal == 1:  # "Higher"
                weight = math.log((alpha * conf + 1e-6) / (1 - beta * conf + 1e-6))
            else:  # "Lower"
                weight = math.log((1 - alpha * conf + 1e-6) / (beta * conf + 1e-6))

            # Scale weight by redundancy factor
            scale = correlation_weights.get(name, 1.0)
            adjusted_weight = weight * scale

            logit += adjusted_weight

        return self.sigmoid(logit)

    def log_odds(self, p):
        return math.log(p / (1 - p))

    def sigmoid(self, x):
        return 1 / (1 + math.exp(-x))

    def adjust_weights_for_correlation(self, expert_predictions, correlation_matrix):
        """
        expert_predictions: list of (name, prediction, confidence)
        correlation_matrix: DataFrame of expert pairwise correlations
        Returns: dict of expert_name -> adjusted_weight_factor (0.0 to 1.0)
        """
        names = [e[0] for e in expert_predictions]
        adjusted_weights = {}

        for name in names:
            others = [n for n in names if n != name]
            if not others:
                adjusted_weights[name] = 1.0
                continue

            avg_corr = np.mean(
                [
                    correlation_matrix.loc[name, o]
                    for o in others
                    if name in correlation_matrix and o in correlation_matrix
                ]
            )
            adjusted_weights[name] = max(
                0.0, 1 - avg_corr
            )  # scale down weight by average correlation

        return adjusted_weights

    def compute_prediction_quality(
        self,
        crowd_prob: float,
        expert_predictions: List[Tuple[str, int, Optional[float]]],
        expert_reliability: Dict[str, float],
    ) -> float:
        num_experts = len(expert_predictions)
        if num_experts == 0:
            return 0.0

        # 1. Expert count score
        count_score = min(num_experts / 5, 1.0)

        # 2. Confidence spread
        confidences = [
            conf if conf is not None else 0.75 for _, _, conf in expert_predictions
        ]
        spread = max(confidences) - min(confidences) if num_experts > 1 else 0
        spread_score = 1.0 - spread

        # 3. Agreement with crowd
        expert_avg = np.mean([pred for _, pred, _ in expert_predictions])
        agreement_score = 1 - abs(expert_avg - crowd_prob)

        # 4. Expert reliability score
        avg_reliability = np.mean(
            [expert_reliability.get(name, 0.6) for name, _, _ in expert_predictions]
        )
        reliability_score = avg_reliability

        # Combine (weighted average)
        final_score = (
            0.25 * count_score
            + 0.25 * spread_score
            + 0.25 * agreement_score
            + 0.25 * reliability_score
        )
        return round(final_score, 4)

    def get_expert_accuracy(self, expert_name: str) -> float:
        """
        Returns the overall accuracy of an expert based on their correct and total predictions.
        Falls back to a neutral default (e.g., 0.6) if the expert is missing or has no predictions.
        """
        try:
            row = self.db.fetch_expert_accuracy(expert_name)

            if row:
                correct, total = row
                if total > 0:
                    return round(correct / total, 4)

            return 0.60  # Default fallback

        except Exception as e:
            print(f"⚠️ Error fetching expert accuracy for {expert_name}: {e}")
            return 0.60

    def combine_predictions(self, bayesian, logistic, quality_score):
        """
        Combine Bayesian and Logistic predictions using quality score as weight.
        """
        return round((quality_score * bayesian + (1 - quality_score) * logistic), 4)

    def log_prediction_output(
        self,
        event_id,
        mean_prob,
        std_dev,
        quality_score,
        logistic_prob,
        combined_prob,
        combined_class,
    ):
        """
        Pretty-print the final prediction summary for the given event.
        Shows individual model outputs and the final combined prediction.
        """
        print(f"\nPrediction for Event ID: {event_id}")
        print(f" - Bayesian Model: {int(mean_prob * 100)}% ± {int(std_dev * 100)}%")
        print(f" - Bayesian Quality_score: {round(quality_score, 2)}")
        print(f" - Logistic Model: {int(logistic_prob * 100)}%")
        print(
            f" - Combined Prob: {combined_prob * 100}% (weighted by quality: {round(quality_score, 2)})"
        )
        print(f" - Final Prediction: {'YES (1)' if combined_class == 1 else 'NO (0)'}")
