import React, { useState } from "react";
import { use<PERSON><PERSON><PERSON>, Link, useLocation, useNavigate } from "react-router-dom";
import { Handicapper } from "../types/handicapper";
import { mockHandicappersData } from "../data/mockData";
import useHandicappers from "../hooks/useHandicappers";
import { HiPencilSquare, HiTrash } from "react-icons/hi2";
import ConfirmDialog from "../components/ConfirmDialog";
import PasswordPrompt from "../components/PasswordPrompt";

const ProfilePage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const location = useLocation();
  const navigate = useNavigate();
  const { handicappers, setHandicappers } = useHandicappers();
  const capperInitial: Handicapper | undefined =
    (location.state as Handicapper) ??
    handicappers.find((h) => h.id === Number(id)) ??
    mockHandicappersData.find((h) => h.id === Number(id));

  if (!capperInitial) {
    return (
      <div className="min-h-screen bg-[#061844] text-white flex flex-col items-center justify-center">
        <Link
          to="/"
          className="inline-block mb-6 bg-[#233e6c] hover:bg-[#1a2d54] text-white font-semibold px-4 py-2 rounded-lg transition-colors shadow-md hover:shadow-lg"
        >
          ← Back to Search
        </Link>
        <p className="text-xl">Handicapper not found.</p>
      </div>
    );
  }

  const [capperData, setCapperData] = useState<Handicapper>(
    capperInitial as Handicapper
  );
  const [isEditing, setIsEditing] = useState(false);
  const [draft, setDraft] = useState<Handicapper>(capperInitial as Handicapper);
  const [showDelete, setShowDelete] = useState(false);
  const [passwordAction, setPasswordAction] = useState<"edit" | "delete" | null>(null);

  const totalFollowers =
    capperData.followers_count.twitter +
    capperData.followers_count.instagram +
    capperData.followers_count.tiktok +
    capperData.followers_count.youtube;

  const performSave = async (password: string) => {
    const payload = { ...draft, password } as any;
    await fetch("/handicappers", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(payload),
    }).catch(() => {});
    setCapperData(draft);
    setIsEditing(false);
  };

  const performDelete = async (password: string) => {
    await fetch(`/handicappers/${capperData.id}`, {
      method: "DELETE",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ password }),
    }).catch(() => {});
    setHandicappers(handicappers.filter((h) => h.id !== capperData.id));
    navigate("/");
  };

  const handlePasswordSubmit = async (password: string) => {
    if (passwordAction === "edit") await performSave(password);
    if (passwordAction === "delete") await performDelete(password);
    setPasswordAction(null);
  };

  return (
    <div className="min-h-screen bg-[#061844] text-white flex flex-col items-center pt-10 pb-10">
      <div className="w-full max-w-4xl px-4">
        <Link
          to="/"
          className="inline-block bg-[#233e6c] hover:bg-[#1a2d54] text-white font-semibold px-4 py-2 rounded-lg transition-colors shadow-md hover:shadow-lg"
        >
          ← Back to Search
        </Link>
        <div className="mt-8 bg-[#233e6c] rounded-lg p-6 shadow-lg">
          <div className="flex justify-end gap-3 mb-4">
            {!isEditing && (
              <>
                <button
                  onClick={() => {
                    setDraft(capperData);
                    setIsEditing(true);
                  }}
                  className="p-2 rounded-full bg-[#1a2d54] text-white cursor-pointer hover:bg-gray-200 hover:text-[#1a2d54] transition-colors"
                >
                  <HiPencilSquare className="w-5 h-5" />
                </button>
                <button
                  onClick={() => setShowDelete(true)}
                  className="p-2 rounded-full bg-[#1a2d54] text-red-500 cursor-pointer hover:bg-red-600 hover:text-white transition-colors"
                >
                  <HiTrash className="w-5 h-5" />
                </button>
              </>
            )}
          </div>
          <div className="flex flex-col md:flex-row gap-6 items-start">
            <img
              src={capperData.profile_image_url}
              alt={capperData.name}
              className="w-40 h-40 object-cover rounded-lg"
            />
            <div className="flex-1">
              {isEditing ? (
                <input
                  type="text"
                  value={draft.name}
                  onChange={(e) => setDraft({ ...draft, name: e.target.value })}
                  className="w-full bg-[#1a2d54] rounded-md p-2 mb-2"
                />
              ) : (
                <h1 className="text-3xl font-bold mb-1 text-[#58C612]">
                  {capperData.name}
                </h1>
              )}
              {isEditing ? (
                <input
                  type="text"
                  value={draft.username}
                  onChange={(e) =>
                    setDraft({ ...draft, username: e.target.value })
                  }
                  className="w-full bg-[#1a2d54] rounded-md p-2 mb-4"
                />
              ) : (
                <p className="text-lg mb-4">@{capperData.username}</p>
              )}
              {isEditing ? (
                <textarea
                  value={draft.bio}
                  onChange={(e) => setDraft({ ...draft, bio: e.target.value })}
                  className="w-full bg-[#1a2d54] rounded-md p-2 mb-4"
                />
              ) : (
                <p className="mb-4 text-gray-300">{capperData.bio}</p>
              )}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-400">Accuracy</span>
                  {isEditing ? (
                    <input
                      type="text"
                      value={draft.accuracy_score}
                      onChange={(e) =>
                        setDraft({
                          ...draft,
                          accuracy_score:
                            parseFloat(
                              e.target.value.replace(/[^0-9.]/g, "")
                            ) || 0,
                        })
                      }
                      className="w-full bg-[#1a2d54] rounded-md p-2"
                    />
                  ) : (
                    <div className="text-lg font-semibold">
                      {capperData.accuracy_score}%
                    </div>
                  )}
                </div>
                <div>
                  <span className="text-gray-400">Units/Month</span>
                  {isEditing ? (
                    <input
                      type="text"
                      value={draft.avg_units_per_month}
                      onChange={(e) =>
                        setDraft({
                          ...draft,
                          avg_units_per_month:
                            parseFloat(
                              e.target.value.replace(/[^0-9.]/g, "")
                            ) || 0,
                        })
                      }
                      className="w-full bg-[#1a2d54] rounded-md p-2"
                    />
                  ) : (
                    <div className="text-lg font-semibold">
                      {capperData.avg_units_per_month}
                    </div>
                  )}
                </div>
                <div>
                  <span className="text-gray-400">Pricing</span>
                  {isEditing ? (
                    <input
                      type="text"
                      value={draft.pricing}
                      onChange={(e) =>
                        setDraft({ ...draft, pricing: e.target.value })
                      }
                      className="w-full bg-[#1a2d54] rounded-md p-2"
                    />
                  ) : (
                    <div className="text-lg font-semibold">
                      {capperData.pricing}
                    </div>
                  )}
                </div>
                <div>
                  <span className="text-gray-400">Group</span>
                  {isEditing ? (
                    <input
                      type="text"
                      value={draft.group_memberships}
                      onChange={(e) =>
                        setDraft({
                          ...draft,
                          group_memberships: e.target.value,
                        })
                      }
                      className="w-full bg-[#1a2d54] rounded-md p-2"
                    />
                  ) : (
                    <div className="text-lg font-semibold">
                      {capperData.group_memberships}
                    </div>
                  )}
                </div>
                <div>
                  <span className="text-gray-400">Customers</span>
                  {isEditing ? (
                    <input
                      type="text"
                      value={draft.estimated_customers}
                      onChange={(e) =>
                        setDraft({
                          ...draft,
                          estimated_customers:
                            parseInt(e.target.value.replace(/[^0-9]/g, "")) ||
                            0,
                        })
                      }
                      className="w-full bg-[#1a2d54] rounded-md p-2"
                    />
                  ) : (
                    <div className="text-lg font-semibold">
                      {capperData.estimated_customers}
                    </div>
                  )}
                </div>
                <div>
                  <span className="text-gray-400">Last Updated</span>
                  <div className="text-lg font-semibold">
                    {new Date(capperData.last_updated).toLocaleDateString()}
                  </div>
                </div>
              </div>
              {isEditing && (
                <div className="mt-6 flex gap-4">
                  <button
                    onClick={() => setPasswordAction("edit")}
                    className="flex-1 bg-green-600 cursor-pointer hover:bg-green-500 text-black font-semibold py-2 rounded-lg transition-colors"
                  >
                    Save
                  </button>
                  <button
                    onClick={() => setIsEditing(false)}
                    className="flex-1 bg-gray-600 cursor-pointer hover:bg-gray-500 text-white font-semibold py-2 rounded-lg transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              )}
            </div>
          </div>
          <div className="mt-8">
            <h2 className="text-2xl font-bold mb-4">Followers Breakdown</h2>
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 text-center">
              <div className="bg-[#1a2d54] rounded-lg p-4">
                <div className="text-sm text-gray-400 mb-1">X</div>
                {isEditing ? (
                  <input
                    type="text"
                    value={draft.followers_count.twitter}
                    onChange={(e) =>
                      setDraft({
                        ...draft,
                        followers_count: {
                          ...draft.followers_count,
                          twitter:
                            parseInt(e.target.value.replace(/[^0-9]/g, "")) ||
                            0,
                        },
                      })
                    }
                    className="w-full bg-[#0d203f] rounded-md p-2 text-center"
                  />
                ) : (
                  <div className="text-lg font-semibold">
                    {capperData.followers_count.twitter.toLocaleString()}
                  </div>
                )}
              </div>
              <div className="bg-[#1a2d54] rounded-lg p-4">
                <div className="text-sm text-gray-400 mb-1">Instagram</div>
                {isEditing ? (
                  <input
                    type="text"
                    value={draft.followers_count.instagram}
                    onChange={(e) =>
                      setDraft({
                        ...draft,
                        followers_count: {
                          ...draft.followers_count,
                          instagram:
                            parseInt(e.target.value.replace(/[^0-9]/g, "")) ||
                            0,
                        },
                      })
                    }
                    className="w-full bg-[#0d203f] rounded-md p-2 text-center"
                  />
                ) : (
                  <div className="text-lg font-semibold">
                    {capperData.followers_count.instagram.toLocaleString()}
                  </div>
                )}
              </div>
              <div className="bg-[#1a2d54] rounded-lg p-4">
                <div className="text-sm text-gray-400 mb-1">TikTok</div>
                {isEditing ? (
                  <input
                    type="text"
                    value={draft.followers_count.tiktok}
                    onChange={(e) =>
                      setDraft({
                        ...draft,
                        followers_count: {
                          ...draft.followers_count,
                          tiktok:
                            parseInt(e.target.value.replace(/[^0-9]/g, "")) ||
                            0,
                        },
                      })
                    }
                    className="w-full bg-[#0d203f] rounded-md p-2 text-center"
                  />
                ) : (
                  <div className="text-lg font-semibold">
                    {capperData.followers_count.tiktok.toLocaleString()}
                  </div>
                )}
              </div>
              <div className="bg-[#1a2d54] rounded-lg p-4">
                <div className="text-sm text-gray-400 mb-1">YouTube</div>
                {isEditing ? (
                  <input
                    type="text"
                    value={draft.followers_count.youtube}
                    onChange={(e) =>
                      setDraft({
                        ...draft,
                        followers_count: {
                          ...draft.followers_count,
                          youtube:
                            parseInt(e.target.value.replace(/[^0-9]/g, "")) ||
                            0,
                        },
                      })
                    }
                    className="w-full bg-[#0d203f] rounded-md p-2 text-center"
                  />
                ) : (
                  <div className="text-lg font-semibold">
                    {capperData.followers_count.youtube.toLocaleString()}
                  </div>
                )}
              </div>
            </div>
            <div className="mt-4 text-center text-sm text-gray-400">
              Total Followers:{" "}
              {(
                draft.followers_count.twitter +
                draft.followers_count.instagram +
                draft.followers_count.tiktok +
                draft.followers_count.youtube
              ).toLocaleString()}
            </div>
          </div>
          <div className="mt-8 flex flex-col sm:flex-row gap-4 text-sm">
            <a
              href={`https://x.com/${capperData.twitter_handle.replace(
                "@",
                ""
              )}`}
              target="_blank"
              rel="noopener noreferrer"
              className="flex-1 bg-blue-500 hover:bg-blue-400 text-black font-semibold py-2 rounded-lg transition-colors text-center"
            >
              Twitter
            </a>
            <a
              href={`https://instagram.com/${capperData.instagram_handle}`}
              target="_blank"
              rel="noopener noreferrer"
              className="flex-1 bg-pink-500 hover:bg-pink-400 text-black font-semibold py-2 rounded-lg transition-colors text-center"
            >
              Instagram
            </a>
            {capperData.tiktok_handle && (
              <a
                href={`https://www.tiktok.com/@${capperData.tiktok_handle}`}
                target="_blank"
                rel="noopener noreferrer"
                className="flex-1 bg-[#69C9D0] hover:bg-[#89d6dd] text-black font-semibold py-2 rounded-lg transition-colors text-center"
              >
                TikTok
              </a>
            )}
            {capperData.youtube_handle && (
              <a
                href={`https://${capperData.youtube_handle}`}
                target="_blank"
                rel="noopener noreferrer"
                className="flex-1 bg-red-600 hover:bg-red-500 text-black font-semibold py-2 rounded-lg transition-colors text-center"
              >
                YouTube
              </a>
            )}
          </div>
        </div>
      </div>

      {showDelete && (
        <ConfirmDialog
          isOpen={showDelete}
          onClose={() => setShowDelete(false)}
          onConfirm={() => {
            setShowDelete(false);
            setPasswordAction("delete");
          }}
          title="Delete Handicapper"
          message="Are you sure you want to delete this handicapper?"
        />
      )}

      <PasswordPrompt
        isOpen={passwordAction !== null}
        onClose={() => setPasswordAction(null)}
        onSubmit={handlePasswordSubmit}
        title="Admin Password Required"
        confirmLabel={passwordAction === "delete" ? "Delete" : "Save"}
      />
    </div>
  );
};

export default ProfilePage;
