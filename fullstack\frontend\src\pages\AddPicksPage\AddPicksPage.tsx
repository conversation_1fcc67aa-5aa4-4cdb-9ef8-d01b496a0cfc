import React, { useState, useEffect } from "react";
import { useAuth } from "../../contexts/AuthContext";
import ViewContainer from "../../components/ViewContainer";
import LoadingScreen from "../../components/LoadingScreen";
import { useTodaysEvents } from "../../hooks/useTodaysEvents";
import {
  convertEventsToPicks,
  convertEventsToHandicappers,
  Handicapper,
} from "../../utils/dataTransforms";

// ===== DATE CONFIGURATION =====
// Set ENABLE_CUSTOM_DATE to true to use a specific date for testing
// Set CUSTOM_DATE to the date you want to test (YYYY-MM-DD format)
// When ENABLE_CUSTOM_DATE is false, it will use the current date
const ENABLE_CUSTOM_DATE = true; // Toggle this to enable/disable custom date
const CUSTOM_DATE = "2025-05-28"; // Set your test date here

function AddPicksPage() {
  const { navigateToView } = useAuth();

  // Determine which date to use based on configuration
  const targetDate = ENABLE_CUSTOM_DATE ? CUSTOM_DATE : undefined;

  // Log configuration on component load
  console.log("🛠️ DATE OVERRIDE CONFIG:");
  console.log("   - Custom date enabled:", ENABLE_CUSTOM_DATE);
  console.log(
    "   - Target date:",
    ENABLE_CUSTOM_DATE ? CUSTOM_DATE : "current date"
  );
  console.log("   - Passing to hook:", targetDate);

  const { events, loading, error, refetch, isCacheUsed } =
    useTodaysEvents(targetDate);
  const [picks, setPicks] = useState<any[]>([]);
  const [handicappers, setHandicappers] = useState<Handicapper[]>([]);
  const [showContent, setShowContent] = useState(false);

  const handleBackToHome = () => {
    navigateToView("home");
  };

  // Convert events to picks format when events data changes
  useEffect(() => {
    if (events.length > 0) {
      console.log("✅ Converting", events.length, "events to picks format");
      const convertedPicks = convertEventsToPicks(events);
      console.log("🎯 Converted picks:", convertedPicks);

      console.log(
        "✅ Converting",
        events.length,
        "events to handicappers format"
      );
      const convertedHandicappers = convertEventsToHandicappers(events);
      console.log("🏆 Converted handicappers:", convertedHandicappers);

      setPicks(convertedPicks);
      setHandicappers(convertedHandicappers);
    } else {
      console.log(
        "❌ No events data received, clearing picks and handicappers"
      );
      setPicks([]); // Clear picks when no events
      setHandicappers([]); // Clear handicappers when no events
    }
  }, [events]);

  // Skip loading animation if cached data is available
  useEffect(() => {
    if (isCacheUsed) {
      console.log("⚡ Cached data available, skipping loading animation");
      setShowContent(true);
    }
  }, [isCacheUsed]);

  // Show loading state only if no cached data is available
  if (loading && !isCacheUsed) {
    return (
      <LoadingScreen
        videoSrc="/project_parlay_loading_anim.mp4"
        isLoading={loading}
        onAnimationComplete={() => setShowContent(true)}
        loadingText="Loading picks..."
        subText={
          ENABLE_CUSTOM_DATE
            ? `📅 Using custom date: ${CUSTOM_DATE}`
            : undefined
        }
      />
    );
  }

  // Show LoadingScreen while loading (without cache) or until animation completes
  if ((loading && !isCacheUsed) || (!showContent && !isCacheUsed)) {
    return (
      <LoadingScreen
        videoSrc="/project_parlay_loading_anim.mp4"
        isLoading={loading}
        onAnimationComplete={() => setShowContent(true)}
        loadingText="Loading picks..."
        subText={
          ENABLE_CUSTOM_DATE
            ? `📅 Using custom date: ${CUSTOM_DATE}`
            : undefined
        }
      />
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-[#061844] text-white flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-400 text-lg mb-4">
            Error loading picks: {error}
          </p>
          <button
            onClick={() => {
              setShowContent(false); // Reset content visibility for potential loading
              refetch();
            }}
            className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <ViewContainer
      onBackToHome={handleBackToHome}
      picks={picks.length > 0 ? picks : []} // Use real data or empty array
      handicappers={handicappers.length > 0 ? handicappers : []} // Use real handicappers data
      events={events}
    />
  );
}

export default AddPicksPage;
