import { useState, useEffect } from "react";
import { Handicapper } from "../types/handicapper";

const KEY = "handicappers_cache_v1";

const readCache = (): Handicapper[] => {
  try {
    const raw = localStorage.getItem(KEY);
    if (raw) return JSON.parse(raw) as Handicapper[];
  } catch {}
  return [];
};

const writeCache = (data: Handicapper[]) => {
  try {
    localStorage.setItem(KEY, JSON.stringify(data));
  } catch {}
};

const checkBackendHealth = async (): Promise<boolean> => {
  try {
    const res = await fetch("/health", { 
      method: "GET",
      timeout: 5000 as any 
    });
    return res.ok;
  } catch {
    return false;
  }
};

export default function useHandicappers() {
  const [handicappers, setHandicappers] = useState<Handicapper[]>(readCache);
  const [backendHealthy, setBackendHealthy] = useState<boolean>(false);

  useEffect(() => {
    const fetchAndUpdate = async () => {
      try {
        const isHealthy = await checkBackendHealth();
        setBackendHealthy(isHealthy);
        
        if (!isHealthy) {
          console.warn("Backend health check failed, using cached data");
          return;
        }

        const res = await fetch("/handicappers");
        if (!res.ok) return;
        const freshData = await res.json();

        if (!Array.isArray(freshData)) {
          console.error(
            "Expected an array from /handicappers, but received:",
            freshData,
          );
          return;
        }

        const fresh = freshData as Handicapper[];
        const cached = readCache();
        const cacheMax = cached.reduce(
          (m, h) => Math.max(m, new Date(h.last_updated).getTime()),
          0
        );
        const freshMax = fresh.reduce(
          (m, h) => Math.max(m, new Date(h.last_updated).getTime() || 0),
          0
        );
        const shouldUpdate = cached.length === 0 || freshMax > cacheMax;
        if (shouldUpdate) {
          setHandicappers(fresh);
          writeCache(fresh);
        }
      } catch (error) {
        console.error("Failed to fetch handicappers:", error);
      }
    };

    fetchAndUpdate();
    const id = setInterval(fetchAndUpdate, 300000);
    return () => clearInterval(id);
  }, []);

  useEffect(() => {
    writeCache(handicappers);
  }, [handicappers]);

  return { handicappers, setHandicappers, backendHealthy } as const;
}
