import React from "react";

interface ToggleSwitchProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  label?: string;
}

/**
 * A reusable toggle switch component styled to match the project's UI theme.
 * Uses Tailwind utility classes for styling (same approach as other components).
 */
const ToggleSwitch: React.FC<ToggleSwitchProps> = ({ checked, onChange, label }) => {
  return (
    <label className="flex items-center cursor-pointer space-x-2 select-none">
      {label && <span className="text-sm text-gray-300">{label}</span>}
      <span
        className={`relative inline-block w-12 h-6 transition-colors duration-300 rounded-full ${checked ? 'bg-[#58C612]' : 'bg-gray-600'}`}
      >
        {/* Hidden checkbox to handle focus & accessibility */}
        <input
          type="checkbox"
          checked={checked}
          onChange={(e) => onChange(e.target.checked)}
          className="absolute w-0 h-0 opacity-0" // hide but keep accessible
        />
        {/* Slider */}
        <span
          className={`absolute left-0 top-0 w-6 h-6 bg-white rounded-full shadow transform transition-transform duration-300 ${checked ? 'translate-x-6' : 'translate-x-0'}`}
        />
      </span>
    </label>
  );
};

export default ToggleSwitch;
