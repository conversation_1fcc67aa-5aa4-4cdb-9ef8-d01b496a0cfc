import React from "react";
import { useNavigate } from "react-router-dom";
import { Handicapper } from "../types/handicapper";

export type SortKey =
  | "name"
  | "username"
  | "twitter_handle"
  | "instagram_handle"
  | "accuracy_score"
  | "avg_units_per_month"
  | "followers_total"
  | "last_updated"
  | "group_memberships"
  | "pricing"
  | null;

export type SortDirection = "ascending" | "descending" | null;

interface HandicapperTableProps {
  data: Handicapper[];
  onSort: (key: SortKey) => void;
  sortConfig: { key: SortKey; direction: SortDirection };
}

const HandicapperTable: React.FC<HandicapperTableProps> = ({
  data,
  onSort,
  sortConfig,
}) => {
  const navigate = useNavigate();

  const getSortIndicator = (key: SortKey) => {
    if (sortConfig.key !== key || !sortConfig.direction) {
      return <span className="ml-1 text-gray-500" />;
    }
    return sortConfig.direction === "ascending" ? (
      <span className="ml-1">↑</span>
    ) : (
      <span className="ml-1">↓</span>
    );
  };

  const headers: { key: Exclude<SortKey, null>; label: string }[] = [
    { key: "name", label: "Name" },
    { key: "username", label: "Username" },
    { key: "twitter_handle", label: "Twitter" },
    { key: "instagram_handle", label: "Instagram" },
    { key: "accuracy_score", label: "Accuracy" },
    { key: "avg_units_per_month", label: "Units/Month" },
    { key: "followers_total", label: "Followers" },
    { key: "group_memberships", label: "Group" },
    { key: "pricing", label: "Pricing" },
    { key: "last_updated", label: "Updated" },
  ];

  const handleRowClick = (handicapper: Handicapper) => {
    navigate(`/profile/${handicapper.id}`, { state: handicapper });
  };
  // column sorting - make more intuitive using icons - easily recognizable to be used
  return (
    <div className="overflow-x-auto w-full max-w-7xl mt-8">
      <table className="min-w-full text-sm text-left text-white border-collapse">
        <thead className="bg-[#233e6c]">
          <tr>
            {headers.map((header) => (
              <th
                key={header.key}
                className="px-4 py-3 font-semibold cursor-pointer hover:bg-[#1a2d54] transition-colors select-none"
                onClick={() => onSort(header.key)}
              >
                {header.label}
                {getSortIndicator(header.key)}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.length === 0 && (
            <tr>
              <td colSpan={10} className="px-4 py-6 text-center text-gray-400">
                No handicappers found.
              </td>
            </tr>
          )}
          {data.map((h, idx) => (
            <tr
              key={h.id}
              className={`bg-${
                idx % 2 === 0 ? "even" : "odd"
              } hover:bg-[#2a4a87] transition-colors cursor-pointer`}
              onClick={() => handleRowClick(h)}
            >
              <td className="px-4 py-3 whitespace-nowrap">{h.name}</td>
              <td className="px-4 py-3 whitespace-nowrap">{h.username}</td>
              <td className="px-4 py-3 whitespace-nowrap text-blue-400 hover:underline">
                {h.twitter_handle}
              </td>
              <td className="px-4 py-3 whitespace-nowrap text-pink-400 hover:underline">
                {h.instagram_handle}
              </td>
              <td className="px-4 py-3 text-center">{h.accuracy_score}%</td>
              <td className="px-4 py-3 text-center">{h.avg_units_per_month}</td>
              <td className="px-4 py-3 text-right">
                {(
                  h.followers_count.twitter +
                  h.followers_count.instagram +
                  h.followers_count.tiktok +
                  h.followers_count.youtube
                ).toLocaleString()}
              </td>
              <td className="px-4 py-3">{h.group_memberships}</td>
              <td className="px-4 py-3">{h.pricing}</td>
              <td className="px-4 py-3 whitespace-nowrap text-gray-400">
                {new Date(h.last_updated).toLocaleDateString()}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default HandicapperTable;
