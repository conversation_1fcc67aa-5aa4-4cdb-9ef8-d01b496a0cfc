import express from "express";
import path from "path";
import { fileURLToPath } from "url";
import { createProxyMiddleware } from "http-proxy-middleware";

const app = express();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const distPath = path.join(__dirname, "..", "dist");

// Proxy API requests to the Flask backend
app.use(
  "/api",
  createProxyMiddleware({
    target: "http://localhost:5000",
    changeOrigin: true,
    // No path rewrite – keep the "/api" prefix so <PERSON>lask sees /api/...
  })
);

app.use(express.static(distPath));

app.get("/{*path}", (req, res) => {
  try {
    res.sendFile(path.join(distPath, "index.html"));
  } catch (error) {
    console.error("Error serving file:", error);
    res.status(500).send("Internal Server Error");
  }
});

const PORT = process.env.PORT || 3000;

const server = app.listen(PORT, () => {
  console.log(`Frontend server running on port ${PORT}`);
});

server.on("error", (err) => {
  console.error("Server error:", err);
  if (err.code === "EADDRINUSE") {
    console.log(`Port ${PORT} is busy, trying ${PORT + 1}`);
    server.listen(PORT + 1);
  } else {
    process.exit(1);
  }
});

process.on("uncaughtException", (err, origin) => {
  console.error("Caught exception:", err);
  console.error("Exception origin:", origin);
});

process.on("unhandledRejection", (reason, promise) => {
  console.error("Unhandled Rejection at:", promise, "reason:", reason);
});

export default app;
