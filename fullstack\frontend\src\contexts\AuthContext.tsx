import React, {
  createContext,
  useState,
  useContext,
  ReactNode,
  useEffect,
  useCallback,
} from "react";
import Login from "../pages/Login/Login"; // Updated import path

export type View =
  | "login"
  | "home"
  | "addPicks"
  | "handicapperProfile"
  | "admin";

interface AuthContextType {
  isAuthenticated: boolean;
  currentView: View;
  login: () => void;
  logout: () => void;
  navigateToView: (view: View) => void;
  navigateBack: () => void;
  navigateForward: () => void;
  canNavigateBack: boolean;
  canNavigateForward: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [history, setHistory] = useState<View[]>(["login"]);
  const [currentHistoryIndex, setCurrentHistoryIndex] = useState<number>(0);
  const [currentView, setCurrentView] = useState<View>(
    history[currentHistoryIndex]
  );
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);

  const updateHistory = useCallback(
    (newView: View, isNewNavigation: boolean = true) => {
      let newHistory = [...history];
      let newIndex = currentHistoryIndex;
      if (isNewNavigation) {
        const newHistorySlice = history.slice(0, currentHistoryIndex + 1);
        const updatedHistory = [...newHistorySlice, newView];
        newHistory = updatedHistory;
        newIndex = updatedHistory.length - 1;
      }
      setHistory(newHistory);
      setCurrentHistoryIndex(newIndex);
      setCurrentView(newView);
      console.log(
        `AuthContext: View changed to ${newView}, Index: ${newIndex}, History: [${newHistory.join(
          ", "
        )}]`
      );
    },
    [history, currentHistoryIndex]
  );

  const login = useCallback(() => {
    setIsAuthenticated(true);
    setCurrentView("home");
    updateHistory("home");
    console.log("AuthContext: Logged in, view: home");
  }, [updateHistory]);

  const logout = useCallback(() => {
    setIsAuthenticated(false);
    setCurrentView("login");
    updateHistory("login");
    console.log("AuthContext: Logged out, view: login");
  }, [updateHistory]);

  const navigateToView = useCallback(
    (view: View, isNewNavigation: boolean = true) => {
      if (view !== "login" && !isAuthenticated) {
        console.warn(
          "AuthContext: Cannot navigate to protected view without authentication"
        );
        return;
      }
      updateHistory(view, isNewNavigation);
    },
    [updateHistory, isAuthenticated]
  );

  const navigateBack = useCallback(() => {
    if (currentHistoryIndex > 0) {
      const prevIndex = currentHistoryIndex - 1;
      const prevView = history[prevIndex];
      setCurrentView(prevView);
      setCurrentHistoryIndex(prevIndex);
      if (prevView === "login") setIsAuthenticated(false);
      else if (
        ["home", "addPicks", "handicapperProfile", "admin"].includes(prevView)
      )
        setIsAuthenticated(true);
      console.log("Navigated Back to:", prevView);
    }
  }, [currentHistoryIndex, history]);

  const navigateForward = useCallback(() => {
    if (currentHistoryIndex < history.length - 1) {
      const nextIndex = currentHistoryIndex + 1;
      const nextView = history[nextIndex];
      setCurrentView(nextView);
      setCurrentHistoryIndex(nextIndex);
      if (nextView === "login") setIsAuthenticated(false);
      else if (
        ["home", "addPicks", "handicapperProfile", "admin"].includes(nextView)
      )
        setIsAuthenticated(true);
      console.log("Navigated Forward to:", nextView);
    }
  }, [currentHistoryIndex, history]);

  useEffect(() => {
    const handleMouseUp = (event: MouseEvent) => {
      if (event.button === 3) {
        if (canNavigateBack) {
          event.preventDefault();
          navigateBack();
        }
      } else if (event.button === 4) {
        if (canNavigateForward) {
          event.preventDefault();
          navigateForward();
        }
      }
    };

    const handleContextMenu = (event: MouseEvent) => {
      if (event.button === 3 || event.button === 4) {
        event.preventDefault();
      }
    };

    window.addEventListener("mouseup", handleMouseUp);
    window.addEventListener("contextmenu", handleContextMenu);

    return () => {
      window.removeEventListener("mouseup", handleMouseUp);
      window.removeEventListener("contextmenu", handleContextMenu);
    };
  }, [navigateBack, navigateForward, currentHistoryIndex, history.length]);

  const canNavigateBack = currentHistoryIndex > 0;
  const canNavigateForward = currentHistoryIndex < history.length - 1;

  // If not authenticated, Login component is rendered which will eventually call the login() function.
  // The Login component itself will be responsible for its UI and form submission.
  // AuthProvider's role is to provide the state and functions, and protect child routes.
  if (!isAuthenticated) {
    // Pass login function to Login component so it can trigger authentication
    return <Login performLogin={login} />;
  }

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        currentView,
        login,
        logout,
        navigateToView,
        navigateBack,
        navigateForward,
        canNavigateBack,
        canNavigateForward,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
