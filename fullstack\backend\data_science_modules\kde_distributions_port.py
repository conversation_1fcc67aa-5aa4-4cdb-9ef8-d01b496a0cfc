from fullstack.backend.data_science_modules.planet_scale_port import get_connection
import json
import pymysql  

def fetch_distributions(event_id: str):
    query = """
        SELECT event_id, player_name, stat_type, game_date,
               pdf, cdf, odds_pdf, odds_cdf, last_updated
        FROM player_distributions
        WHERE event_id = %s
    """
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute(query, (event_id,))
        row = cursor.fetchone()
        if row is None:
            return None
        return {
                "event_id": row[0],
                "player_name": row[1],
                "stat_type": row[2],
                "game_date": str(row[3]),
                "pdf": json.loads(row[4]),
                "cdf": json.loads(row[5]),
                "odds_pdf": json.loads(row[6]),
                "odds_cdf": json.loads(row[7]),
                "last_updated": str(row[8])
            }

    
def import_distribution(
    event_id: str,
    player_name: str,
    stat_type: str,
    game_date: str,
    pdf_json: dict,
    cdf_json: dict,
    odds_pdf_json: dict,
    odds_cdf_json: dict
):
    
    query = """
        INSERT INTO player_distributions (
            event_id, player_name, stat_type, game_date,
            pdf, cdf, odds_pdf, odds_cdf
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            player_name = VALUES(player_name),
            stat_type = VALUES(stat_type),
            game_date = VALUES(game_date),
            pdf = VALUES(pdf),
            cdf = VALUES(cdf),
            odds_pdf = VALUES(odds_pdf),
            odds_cdf = VALUES(odds_cdf),
            last_updated = CURRENT_TIMESTAMP
    """
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute(query, (
            event_id,
            player_name,
            stat_type,
            game_date,
            json.dumps(pdf_json),
            json.dumps(cdf_json),
            json.dumps(odds_pdf_json),
            json.dumps(odds_cdf_json)
        ))
        conn.commit()