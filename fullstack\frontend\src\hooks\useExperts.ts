import { useState, useEffect } from "react";
import { fetchExperts, fetchTodaysEvents } from "../utils/api";

interface UseExpertsReturn {
  experts: string[];
  loading: boolean;
  error: string | null;
}

export const useExperts = (): UseExpertsReturn => {
  const [experts, setExperts] = useState<string[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadExperts = async (): Promise<void> => {
      try {
        setLoading(true);
        setError(null);
        let combined: string[] = [];

        // 1. Try to fetch standalone experts list
        try {
          const resp = await fetchExperts();
          if (resp.success) combined = resp.experts;
        } catch (e) {
          // ignore 404 or network errors, use events fallback
        }

        // 2. Fetch today's events to extract handicapper names
        try {
          const eventsResp = await fetchTodaysEvents();
          if (eventsResp.success) {
            eventsResp.events.forEach((ev) => {
              if (Array.isArray(ev.handicappers)) {
                combined.push(...ev.handicappers);
              }
            });
          }
        } catch (e) {
          // ignore errors in events fetch – optional
        }

        // Deduplicate & sort
        const unique = Array.from(new Set(combined)).sort();
        setExperts(unique);
      } catch (err) {
        const msg = err instanceof Error ? err.message : "Unknown error";
        if (msg.includes("404")) {
          setExperts([]);
        } else {
          setError(msg);
        }
      } finally {
        setLoading(false);
      }
    };

    loadExperts();
  }, []);

  return { experts, loading, error };
};
