import { ReactNode } from "react";
import { createPortal } from "react-dom";

interface PickTooltipProps {
  left: number;
  top: number;
  children: ReactNode;
}

export default function PickTooltip({ left, top, children }: PickTooltipProps) {
  return createPortal(
    <div
      className="fixed z-[9999999] pointer-events-none transform -translate-x-1/2 -translate-y-full"
      style={{ left, top }}
    >
      {children}
    </div>,
    document.body
  );
} 