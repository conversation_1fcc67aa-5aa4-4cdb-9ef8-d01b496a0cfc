import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
  useRef,
  useCallback,
} from "react";
import SmartCache from "../utils/cache";

export interface FavoriteHandicapper {
  id: number;
  name: string;
  accuracy: string;
}

interface FavoritesContextType {
  favorites: number[];
  favoriteHandicappers: FavoriteHandicapper[];
  toggleFavorite: (id: number, name?: string, accuracy?: string) => void;
  isFavorite: (id: number) => boolean;
  getFavoriteHandicapperName: (id: number) => string;
}

const FavoritesContext = createContext<FavoritesContextType | undefined>(
  undefined
);

interface FavoritesProviderProps {
  children: ReactNode;
}

const favoritesCache = new SmartCache("favorite_handicappers", {
  freshDuration: 24 * 60 * 60 * 1000,
  maxAge: 30 * 24 * 60 * 60 * 1000,
  version: "1.0.0",
});

export function FavoritesProvider({ children }: FavoritesProviderProps) {
  const [favoriteHandicappers, setFavoriteHandicappers] = useState<
    FavoriteHandicapper[]
  >(() => {
    const { data } =
      favoritesCache.get<FavoriteHandicapper[]>("user_favorites");
    if (data && Array.isArray(data)) {
      console.log(
        "FavoritesContext: Initialized",
        data.length,
        "favorites from cache"
      );
      return data;
    }
    return [];
  });

  const favorites = favoriteHandicappers.map((h) => h.id);

  const isFirstRender = useRef(true);
  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    favoritesCache.set(favoriteHandicappers, "user_favorites");
    console.log(
      "FavoritesContext: Saved",
      favoriteHandicappers.length,
      "favorites to cache"
    );
  }, [favoriteHandicappers]);

  const toggleFavorite = useCallback(
    (id: number, name?: string, accuracy?: string) => {
      setFavoriteHandicappers((prev) => {
        const existing = prev.find((h) => h.id === id);

        if (existing) {
          console.log("FavoritesContext: Removed favorite:", id);
          return prev.filter((h) => h.id !== id);
        } else {
          if (prev.length >= 5) {
            alert(
              "You can only have up to 5 favorite handicappers. Please remove one first."
            );
            return prev;
          }
          console.log("FavoritesContext: Added favorite:", id);
          const newFavorite: FavoriteHandicapper = {
            id,
            name: name || `Handicapper ${id}`,
            accuracy: accuracy || "N/A",
          };
          return [...prev, newFavorite];
        }
      });
    },
    []
  );

  const isFavorite = useCallback(
    (id: number): boolean => {
      return favoriteHandicappers.some((h) => h.id === id);
    },
    [favoriteHandicappers]
  );

  const getFavoriteHandicapperName = useCallback(
    (id: number): string => {
      const handicapper = favoriteHandicappers.find((h) => h.id === id);
      return handicapper?.name || `Handicapper ${id}`;
    },
    [favoriteHandicappers]
  );

  const value: FavoritesContextType = {
    favorites,
    favoriteHandicappers,
    toggleFavorite,
    isFavorite,
    getFavoriteHandicapperName,
  };

  return (
    <FavoritesContext.Provider value={value}>
      {children}
    </FavoritesContext.Provider>
  );
}

export function useFavorites(): FavoritesContextType {
  const context = useContext(FavoritesContext);
  if (context === undefined) {
    throw new Error("useFavorites must be used within a FavoritesProvider");
  }
  return context;
}
