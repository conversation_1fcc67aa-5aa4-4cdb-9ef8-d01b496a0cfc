import React from "react";
import Overlay from "./Overlay";

interface ConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmLabel?: string;
}

const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmLabel,
}) => {
  if (!isOpen) return null;

  return (
    <Overlay onClose={onClose}>
      <div className="text-white max-w-sm text-center">
        <h2 className="text-2xl font-bold mb-4 text-[#58C612]">{title}</h2>
        <p className="mb-6">{message}</p>
        <div className="flex justify-center gap-4">
          <button
            onClick={onClose}
            className="px-6 py-2 rounded-lg bg-gray-600 hover:bg-gray-500 transition-colors cursor-pointer"
          >
            Go Back
          </button>
          <button
            onClick={onConfirm}
            className="px-6 py-2 rounded-lg bg-[#58C612] text-black font-bold hover:bg-green-400 transition-colors cursor-pointer"
          >
            {confirmLabel || "Yes, I'm Sure"}
          </button>
        </div>
      </div>
    </Overlay>
  );
};

export default ConfirmDialog;
