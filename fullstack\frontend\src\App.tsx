import React from "react";
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import HomePage from "./pages/HomePage/HomePage.tsx";
import Login from "./pages/Login/Login.tsx";
import { useAuth } from "./contexts/AuthContext.tsx";
import AddPicksPage from "./pages/AddPicksPage/AddPicksPage.tsx";
import Sidebar from "./components/Sidebar";
import HandicapperProfile from "./pages/HandicapperProfile";
import AdminManualPick from "./pages/AdminManualPick";

function App() {
  // Handle direct URL access (e.g., /admin) without full React Router integration
  const directPath = window.location.pathname;
  if (directPath === "/admin" || directPath.startsWith("/admin/")) {
    return <AdminManualPick />;
  }

  const { currentView, login } = useAuth();

  let page: React.ReactNode;
  switch (currentView) {
    case "login":
      page = <Login performLogin={login} />;
      break;
    case "home":
      page = <HomePage />;
      break;
    case "addPicks":
      page = <AddPicksPage />;
      break;
    case "handicapperProfile":
      page = <HandicapperProfile />;
      break;
    case "admin":
      page = <AdminManualPick />;
      break;
    default:
      page = <Login performLogin={login} />;
  }

  return (
    <>
      <Sidebar />
      {page}
      <ToastContainer
        position="bottom-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="dark"
      />
    </>
  );
}

export default App;
