import React, { useState, useEffect } from "react";
import { ManualPick, ExpertConfidence } from "../../types/manualPick";
import { Expert } from "../../types/expert";
import { insertEvent } from "../../utils/api";
import { getExpertsByDate } from "../../api/expertPredictions";
import DateSelector from "../DateSelector/DateSelector";
import { toast } from "react-toastify";
import ToggleSwitch from "../ToggleSwitch/ToggleSwitch";

interface ManualPickFormProps {
  adminPassword: string;
}

const CUSTOM_SENTINEL = "__CUSTOM__";

const ManualPickForm: React.FC<ManualPickFormProps> = ({ adminPassword }) => {
  const [selectedDate, setSelectedDate] = useState<string>(
    new Date().toISOString().split("T")[0]
  );
  const [dateFilteredExperts, setDateFilteredExperts] = useState<Expert[]>([]);
  const [expertsLoading, setExpertsLoading] = useState<boolean>(true);
  const [expertsError, setExpertsError] = useState<string | null>(null);
  const [isDatePinned, setIsDatePinned] = useState<boolean>(false);

  const [form, setForm] = useState<ManualPick>({
    name: "",
    odds: 1.0,
    league: [],
    pick_origin: [],
    reusable: true,
    capital_limit: 0,
    mutual_exclusion: -1,
    pick_type: "MoneyLine",
    player_team: "",
    stat_type: "MoneyLine",
    prediction: 1,
    player_name: "",
    stat_threshold: undefined,
    eventDate: selectedDate,
  });

  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [customInputs, setCustomInputs] = useState<Record<number, string>>({});

  const availableLeagues: string[] = ["NBA", "NFL", "MLB", "NHL"];

  useEffect(() => {
    const pinnedDate = localStorage.getItem("pinnedEventDate");
    if (pinnedDate) {
      setSelectedDate(pinnedDate);
      setIsDatePinned(true);
    }
  }, []);

  useEffect(() => {
    const fetchExpertsForDate = async () => {
      setExpertsLoading(true);
      setExpertsError(null);
      try {
        const experts = await getExpertsByDate(selectedDate);
        setDateFilteredExperts(experts);
      } catch (error) {
        console.error("Failed to fetch experts for date:", error);
        setExpertsError(
          error instanceof Error ? error.message : "An unknown error occurred"
        );
      } finally {
        setExpertsLoading(false);
      }
    };

    fetchExpertsForDate();
  }, [selectedDate]);

  const handleInputChange = (
    field: keyof ManualPick,
    value: string | number | boolean | string[] | undefined
  ): void => {
    setForm((prev) => ({ ...prev, [field]: value }));
  };

  const handleLeagueToggle = (league: string): void => {
    setForm((prev) => ({
      ...prev,
      league: prev.league.includes(league)
        ? prev.league.filter((l) => l !== league)
        : [...prev.league, league],
    }));
  };

  const handleExpertChange = (
    index: number,
    field: keyof ExpertConfidence,
    value: string | number
  ): void => {
    setForm((prev) => ({
      ...prev,
      pick_origin: prev.pick_origin.map((expert, i) =>
        i === index ? { ...expert, [field]: value } : expert
      ),
    }));
  };

  const handleCustomInputChange = (index: number, value: string): void => {
    setCustomInputs((prev) => ({ ...prev, [index]: value }));
  };

  const addExpert = (): void => {
    setForm((prev) => ({
      ...prev,
      pick_origin: [...prev.pick_origin, { name: "", confidence: 75 }],
    }));
  };

  const removeExpert = (index: number): void => {
    setForm((prev) => ({
      ...prev,
      pick_origin: prev.pick_origin.filter((_, i) => i !== index),
    }));
    setCustomInputs((prev) => {
      const newInputs = { ...prev };
      delete newInputs[index];
      return newInputs;
    });
  };

  const handleDateLockToggle = (locked: boolean): void => {
    setIsDatePinned(locked);
    if (locked) {
      localStorage.setItem("pinnedEventDate", selectedDate);
      toast.info("Date locked!");
    } else {
      localStorage.removeItem("pinnedEventDate");
      toast.info("Date unlocked!");
    }
  };

  const handleSubmit = async (
    e: React.FormEvent<HTMLFormElement>
  ): Promise<void> => {
    e.preventDefault();

    const resolvedPickOrigin = form.pick_origin.map((expert, index) => ({
      ...expert,
      name:
        expert.name === CUSTOM_SENTINEL
          ? customInputs[index] || ""
          : expert.name,
    }));

    if (
      !form.name ||
      form.odds <= 0 ||
      form.league.length === 0 ||
      resolvedPickOrigin.length === 0 ||
      resolvedPickOrigin.some((expert) => !expert.name.trim())
    ) {
      toast.error("Please fill all required fields including expert names");
      return;
    }

    if (
      form.pick_type === "StatOverUnder" &&
      (!form.player_name || form.stat_threshold === undefined)
    ) {
      toast.error(
        "Player name and stat threshold are required for Stat Over/Under picks"
      );
      return;
    }

    setIsSubmitting(true);

    try {
      const submissionData: ManualPick = {
        ...form,
        pick_origin: resolvedPickOrigin,
        admin_password: adminPassword,
        eventDate: selectedDate,
      };

      const response = await insertEvent(submissionData);
      if (response.success) {
        toast.success("Pick submitted successfully!");
        const preservedState = {
            reusable: form.reusable,
            capital_limit: form.capital_limit,
            mutual_exclusion: form.mutual_exclusion,
            eventDate: selectedDate,
        };
        setForm({
          name: "",
          odds: 1.0,
          league: [],
          pick_origin: [],
          pick_type: "MoneyLine",
          player_team: "",
          stat_type: "MoneyLine",
          prediction: 1,
          player_name: "",
          stat_threshold: undefined,
          ...preservedState,
        });
        setCustomInputs({});
      } else {
        toast.error(`Error: ${response.message || "Submission failed"}`);
      }
    } catch (error) {
      toast.error(`Network error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const errorBanner = expertsError ? (
    <div className="text-center text-red-400 mb-4">
      Error loading experts: {expertsError}
    </div>
  ) : null;

  return (
    <div className="max-w-2xl mx-auto p-6 bg-gray-900 bg-opacity-80 border border-[#58C612] rounded-xl shadow-lg text-white">
      <h2 className="text-2xl font-bold mb-6 text-center text-[#58C612]">
        Manual Pick Form
      </h2>

      {errorBanner}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="flex items-center gap-4">
          <div className="flex-grow">
            <DateSelector
              label="Event Date *"
              value={selectedDate}
              onChange={setSelectedDate}
            />
          </div>
          <ToggleSwitch
            label="Lock Date"
            checked={isDatePinned}
            onChange={handleDateLockToggle}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Pick Name *
          </label>
          <input
            type="text"
            value={form.name}
            onChange={(e) => handleInputChange("name", e.target.value)}
            className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
            placeholder="e.g., Lakers vs Warriors"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Decimal Odds *
          </label>
          <input
            type="text"
            value={form.odds}
            onChange={(e) => {
              const value = e.target.value;
              if (/^\d*\.?\d*$/.test(value)) {
                const numValue = parseFloat(value) || 0;
                handleInputChange("odds", numValue);
              }
            }}
            className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
            placeholder="e.g., 1.85"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Leagues *
          </label>
          <div className="flex flex-wrap gap-2">
            {availableLeagues.map((league) => (
              <button
                key={league}
                type="button"
                onClick={() => handleLeagueToggle(league)}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors cursor-pointer ${
                  form.league.includes(league)
                    ? "bg-[#58C612] text-black"
                    : "bg-[#233e6c] text-white hover:bg-[#1a2d54]"
                }`}
              >
                {league}
              </button>
            ))}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Pick Type
          </label>
          <select
            value={form.pick_type}
            onChange={(e) =>
              handleInputChange(
                "pick_type",
                e.target.value as "MoneyLine" | "StatOverUnder"
              )
            }
            className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
          >
            <option value="MoneyLine">MoneyLine</option>
            <option value="StatOverUnder">Stat Over/Under</option>
          </select>
        </div>

        {form.pick_type === "StatOverUnder" && (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Player Name *
              </label>
              <input
                type="text"
                value={form.player_name || ""}
                onChange={(e) =>
                  handleInputChange("player_name", e.target.value)
                }
                className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
                placeholder="e.g., LeBron James"
                required={form.pick_type === "StatOverUnder"}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Player/Team
              </label>
              <input
                type="text"
                value={form.player_team}
                onChange={(e) =>
                  handleInputChange("player_team", e.target.value)
                }
                className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
                placeholder="e.g., LAL, MIA"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Stat Type
              </label>
              <input
                type="text"
                value={form.stat_type}
                onChange={(e) => handleInputChange("stat_type", e.target.value)}
                className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
                placeholder="e.g., Points, Rebounds, Assists"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Stat Threshold *
              </label>
              <input
                type="text"
                value={form.stat_threshold || ""}
                onChange={(e) => {
                  const value = e.target.value;
                  if (/^\d*\.?\d*$/.test(value)) {
                    const numValue = value ? parseFloat(value) : undefined;
                    handleInputChange("stat_threshold", numValue);
                  }
                }}
                className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
                placeholder="e.g., 25.5"
                required={form.pick_type === "StatOverUnder"}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Prediction
              </label>
              <select
                value={form.prediction}
                onChange={(e) =>
                  handleInputChange(
                    "prediction",
                    parseInt(e.target.value) as 1 | 0
                  )
                }
                className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
              >
                <option value={1}>Over/Higher</option>
                <option value={0}>Under/Lower</option>
              </select>
            </div>
          </>
        )}

        <div>
          <div className="flex justify-between items-center mb-2">
            <label className="block text-sm font-medium text-gray-300">
              Expert Predictions *
            </label>
            <button
              type="button"
              onClick={addExpert}
              className="px-3 py-1 bg-[#58C612] text-black text-sm rounded-md hover:bg-[#449e10] transition-colors cursor-pointer"
            >
              Add Expert
            </button>
          </div>

          {expertsLoading ? (
            <div className="text-center text-gray-400">Loading experts for selected date...</div>
          ) : form.pick_origin.map((expert, index) => (
            <div
              key={index}
              className="space-y-2 mb-4 p-3 bg-[#1a2d54] rounded-md"
            >
              <div className="flex gap-2 items-center">
                <select
                  value={expert.name}
                  onChange={(e) =>
                    handleExpertChange(index, "name", e.target.value)
                  }
                  className="flex-1 bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
                >
                  <option value="">Select an Expert</option>
                  {dateFilteredExperts.map((exp) => (
                    <option key={exp.id} value={exp.name}>
                      {exp.name}
                    </option>
                  ))}
                  <option value={CUSTOM_SENTINEL}>-- Add Custom --</option>
                </select>

                {expert.name === CUSTOM_SENTINEL && (
                  <input
                    type="text"
                    value={customInputs[index] || ""}
                    onChange={(e) =>
                      handleCustomInputChange(index, e.target.value)
                    }
                    className="w-full bg-[#233e6c] rounded-md p-2.5 mt-2 focus:ring-2 focus:ring-[#58C612] outline-none"
                    placeholder="Enter custom expert name"
                    required
                  />
                )}

                <input
                  type="range"
                  min="0"
                  max="100"
                  value={expert.confidence}
                  onChange={(e) =>
                    handleExpertChange(
                      index,
                      "confidence",
                      parseInt(e.target.value)
                    )
                  }
                  className="w-32 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                />
                <span className="text-sm w-10 text-center">
                  {expert.confidence}%
                </span>

                <button
                  type="button"
                  onClick={() => removeExpert(index)}
                  className="px-2 py-1 bg-red-600 text-white text-xs rounded-md hover:bg-red-700 transition-colors cursor-pointer"
                >
                  Remove
                </button>
              </div>
            </div>
          ))}
        </div>

        <div className="border-t border-gray-700 pt-6 space-y-4">
          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={form.reusable}
                onChange={(e) => handleInputChange("reusable", e.target.checked)}
                className="h-4 w-4 rounded border-gray-300 text-[#58C612] focus:ring-[#58C612]"
              />
              <span className="ml-2 text-gray-300">Reusable Pick</span>
            </label>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Capital Limit
            </label>
            <input
              type="number"
              value={form.capital_limit}
              onChange={(e) =>
                handleInputChange("capital_limit", parseInt(e.target.value))
              }
              className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
              placeholder="0 for no limit"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Mutual Exclusion Group
            </label>
            <input
              type="number"
              value={form.mutual_exclusion}
              onChange={(e) =>
                handleInputChange("mutual_exclusion", parseInt(e.target.value))
              }
              className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
              placeholder="-1 for no exclusion"
            />
          </div>
        </div>

        <button
          type="submit"
          disabled={isSubmitting || expertsLoading}
          className="w-full py-3 px-4 bg-[#58C612] text-black font-bold rounded-md hover:bg-[#449e10] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-[#58C612] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSubmitting ? "Submitting..." : "Submit Pick"}
        </button>
      </form>
    </div>
  );
};

export default ManualPickForm;
