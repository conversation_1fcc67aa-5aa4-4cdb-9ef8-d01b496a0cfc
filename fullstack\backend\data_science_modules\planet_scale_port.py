import pymysql
pymysql.install_as_MySQLdb()
import os
from dotenv import load_dotenv
from datetime import datetime
from typing import Optional, List, Tuple
from pymysql.cursors import DictCursor
from fullstack.backend.data_science_modules.goalserve_test import fetch_MLB_team_stats, get_todays_events_with_handicappers as _gtewh
import json
# Load environment variables
load_dotenv()

def get_connection():
    return pymysql.connect(
        host=os.getenv("DATABASE_HOST"),
        user=os.getenv("DATABASE_USERNAME"),
        password=os.getenv("DATABASE_PASSWORD"),
        database=os.getenv("DATABASE"),
        ssl={"ssl_ca": "/etc/ssl/cert.pem"}  # Path to trusted CA cert on macOS
    )

def get_dict_connection():
    return pymysql.connect(
        host=os.getenv("DATABASE_HOST"),
        user=os.getenv("DATABASE_USERNAME"),
        password=os.getenv("DATABASE_PASSWORD"),
        database=os.getenv("DATABASE"),
        ssl={"ssl_ca": "/etc/ssl/cert.pem"},  # Path to trusted CA cert on macOS
        cursorclass=DictCursor
    )

def add_event(event_id: str, event_date: str, league: str, team_a: str, team_b: str, actual_result: Optional[int] = None):
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            INSERT IGNORE INTO events (event_id, event_date, league, team_a, team_b, actual_result)
            VALUES (%s, %s, %s, %s, %s, %s)
        """, (event_id, event_date, league, team_a, team_b, actual_result))




def insert_nba_player_log(
    player_name: str,
    game_date: str,
    team: Optional[str] = None,
    opponent: Optional[str] = None,
    points: Optional[float] = None,
    rebounds: Optional[float] = None,
    assists: Optional[float] = None,
    personal_fouls: Optional[float] = None,
    threes_made: Optional[float] = None,
    blocks: Optional[float] = None,
    steals: Optional[float] = None,
    turnovers: Optional[float] = None,
    minutes: Optional[float] = None
):
    print("🏀 Inserting NBA player log:", player_name)
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            INSERT INTO NBA_Player_Logs (
                player_name, game_date, team, opponent,
                points, rebounds, assists, personal_fouls, threes_made,
                blocks, steals, turnovers, minutes
            )
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            player_name, game_date, team, opponent,
            points, rebounds, assists, personal_fouls, threes_made,
            blocks, steals, turnovers, minutes
        ))

def bulk_import_mlb_logs(api_key, start_date, end_date):
    current = start_date

    while current <= end_date:
        date_api = current.strftime("%d.%m.%Y")
        game_date = current.strftime("%Y-%m-%d")
        print(f"\n Processing {game_date}...")

        all_stats = []

        for team in [
            "Braves", "Marlins", "Mets", "Phillies", "Nationals",
            "Cubs", "Pirates", "Reds", "Brewers", "Cardinals",
            "Yankees", "Red Sox", "Rays", "Blue Jays", "Orioles",
            "Astros", "Rangers", "Angels", "Athletics", "Mariners",
            "Rockies", "Diamondbacks", "Dodgers", "Giants",
            "Padres", "Royals", "Twins", "White Sox", "Tigers"
        ]:
            try:
                stats = fetch_MLB_team_stats(api_key, date_api, team)

                if not stats:
                    continue  # Skip teams with no data

                for stat in stats:
                    stat["game_date"] = game_date  # SQL format
                all_stats.extend(stats)

            except Exception as e:
                print(f"❌ Failed to fetch stats for {team} on {date_api}: {e}")

        try:
            inserted = bulk_insert_mlb_logs(all_stats)
            print(f"✅ Inserted {inserted} logs for {game_date}")
        except Exception as insert_err:
            print(f"❌ Bulk insert error on {game_date}: {insert_err}")

        current += timedelta(days=1)

    print("\n Done importing MLB logs.")

def bulk_insert_mlb_logs(logs: List[dict]):
    if not logs:
        print(" No logs insert.")
        return 0

    rows = []
    for stat in logs:
        #print(stat)
        row = (
            stat.get("player_name"),
            stat.get("game_date"),
            stat.get("isHitter"),
            stat.get("isPitcher"),
            stat.get("team"),
            stat.get("opponent"),
            stat.get("hits"),
            stat.get("singles"),
            stat.get("doubles"),
            stat.get("triples"),
            stat.get("runs"),
            stat.get("rbi"),
            stat.get("home_runs"),
            stat.get("walks_hitter"),
            stat.get("hit_by_pitch_hitter"),
            stat.get("at_bats_hitter"),
            stat.get("hit_by_pitch_pitcher"),
            stat.get("fantasy_points_hitter"),
            stat.get("strikeouts_hitter"),
            stat.get("total_bases"),
            stat.get("stolen_bases"),
            stat.get("earned_runs"),
            stat.get("total_pitches"),
            stat.get("strikeouts_pitcher"),
            stat.get("outs_pitched"),
            stat.get("hits_allowed"),
            stat.get("win_credited"),
            stat.get("quality_start")
        )
        # if not enough or too many stats 
        if len(row) != 28: 
            print(f"❗ Skipping row for {stat.get('player_name')} on {stat.get('game_date')}: expected 28 fields, got {len(row)}")
            #print(row)
            return
            continue
        # if not type string, int, float, null
        if any(type(val) not in [str, int, float, type(None)] for val in row):
            print(f" Invalid value types in row: {row}")
            print(f"❗ Skipping row for {stat.get('player_name')} on {stat.get('game_date')}: contains unsupported data types")
            continue

        rows.append(row)

    if not rows:
        print("no valid rows to insert")
        return 0

    try:
        with get_connection() as conn:
            cursor = conn.cursor()
            for row in rows:
                print(f" Inserting row: {row}")
                cursor.execute("""
                    INSERT INTO MLB_Player_Logs (
                        player_name, game_date, isHitter, isPitcher,
                        team, opponent,
                        hits, singles, doubles, triples, runs, rbi, home_runs,
                        walks_hitter, hit_by_pitch_hitter, at_bats_hitter,
                        hit_by_pitch_pitcher, fantasy_points_hitter, strikeouts_hitter, total_bases,
                        stolen_bases, earned_runs, total_pitches, strikeouts_pitcher,
                        outs_pitched, hits_allowed, win_credited, quality_start
                    ) VALUES (
                        %s, %s, %s, %s, 
                        %s, %s,
                        %s, %s, %s, %s, %s, %s, %s,
                        %s, %s, %s, 
                        %s, %s, %s, %s,
                        %s, %s, %s, %s, 
                        %s, %s, %s, %s
                    )
                """, row)
            conn.commit()
        return len(rows)

    except Exception as e:
        print(f"❌ Bulk insert failed: {e}")
        return 0


def update_nba_derived_fields():
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("SELECT entry_id, points, rebounds, assists, blocks, steals, turnovers FROM NBA_Player_Logs")
        rows = cursor.fetchall()

        for row in rows:
            entry_id, points, rebounds, assists, blocks, steals, turnovers = row
            points = points or 0
            rebounds = rebounds or 0
            assists = assists or 0
            blocks = blocks or 0
            steals = steals or 0
            turnovers = turnovers or 0

            pa = points + assists
            pr = points + rebounds
            ra = rebounds + assists
            pra = pa + rebounds
            fantasy_points = (
                points * 1 +
                rebounds * 1.2 +
                assists * 1.5 +
                blocks * 3 +
                steals * 3 +
                turnovers * (-1)
            )

            cursor.execute("""
                UPDATE NBA_Player_Logs
                SET pa = %s, pr = %s, ra = %s, pra = %s, fantasy_points = %s
                WHERE entry_id = %s
            """, (pa, pr, ra, pra, fantasy_points, entry_id))

def add_crowd_prediction(event_id: str, crowd_probability: float, source_name: str = "CrowdConsensus"):
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            INSERT INTO crowd_predictions (event_id, crowd_probability, source_name)
            VALUES (%s, %s, %s)
            ON DUPLICATE KEY UPDATE crowd_probability = VALUES(crowd_probability)
        """, (event_id, crowd_probability, source_name))

def add_expert_prediction(event_id: str, expert_name: str, prediction: int, confidence: Optional[float] = None):
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            INSERT INTO expert_predictions (event_id, expert_name, prediction, confidence)
            VALUES (%s, %s, %s, %s)
        """, (event_id, expert_name, prediction, confidence))

def add_multiple_expert_predictions(predictions: List[Tuple[str, str, int, Optional[float]]]):
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            INSERT INTO expert_predictions (event_id, expert_name, prediction, confidence)
            VALUES (%s, %s, %s, %s)
        """, predictions)

def generate_event_id(pick_name: str, league: str) -> str:
    clean = pick_name.upper().replace(" ", "").replace("/", "-")
    return f"{datetime.today().strftime('%Y-%m-%d')}-{league}-{clean}"

def generate_admin_event_id(event_date, league, pick_type, team_a, team_b, player_name=None, stat_threshold=None, stat_type=None):
    """
    Generate consistent event IDs for admin form in the format: YYYY-MM-DD-LEAGUE-IDENTIFIER

    For player stat picks: YYYY-MM-DD-NBA-PLAYERNAME##.#STATTYPE
    For team matchups: YYYY-MM-DD-NBA-TEAMA-TEAMB
    """
    # Ensure all components are uppercase
    league = league.upper()

    if pick_type == "StatOverUnder" and player_name and stat_threshold is not None and stat_type:
        # Player stat pick format: YYYY-MM-DD-NBA-PLAYERNAME##.#STATTYPE
        # Clean player name: remove spaces, convert to uppercase
        clean_player_name = player_name.replace(" ", "").upper()
        clean_stat_type = stat_type.replace(" ", "").upper()

        # Format stat threshold to ensure proper decimal format
        if isinstance(stat_threshold, (int, float)):
            threshold_str = f"{stat_threshold:.1f}" if stat_threshold % 1 != 0 else f"{int(stat_threshold)}.0"
        else:
            threshold_str = str(stat_threshold)

        event_id = f"{event_date}-{league}-{clean_player_name}{threshold_str}{clean_stat_type}"
    else:
        # Team matchup format: YYYY-MM-DD-NBA-TEAMA-TEAMB
        clean_team_a = team_a.replace(" ", "").upper()
        clean_team_b = team_b.replace(" ", "").upper()
        event_id = f"{event_date}-{league}-{clean_team_a}-{clean_team_b}"

    return event_id

def delete_event(event_id: str):
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("DELETE FROM expert_predictions WHERE event_id = %s", (event_id,))
        cursor.execute("DELETE FROM crowd_predictions WHERE event_id = %s", (event_id,))
        cursor.execute("DELETE FROM events WHERE event_id = %s", (event_id,))
    print(f"Deleted event {event_id} and all associated predictions.")

def fetch_events_by_date(event_date: str):
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT event_id, league, team_a, team_b, actual_result
            FROM events
            WHERE event_date = %s
        """, (event_date,))
        rows = cursor.fetchall()

    print(f"Events on {event_date}:")
    for row in rows:
        event_id, league, team_a, team_b, result = row
        result_text = "Occurred" if result == 1 else "Did Not Occur" if result == 0 else "Unknown"
        print(f" - [{league}] {team_a} vs {team_b} (Event ID: {event_id}) → {result_text}")

def fetch_predictions_by_expert(expert_name: str):
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT event_id, prediction, confidence, prediction_time
            FROM expert_predictions
            WHERE expert_name = %s
            ORDER BY prediction_time DESC
        """, (expert_name,))
        rows = cursor.fetchall()

    print(f"Predictions by {expert_name}:")
    for event_id, prediction, confidence, time in rows:
        pred_str = "Too Low" if prediction == 1 else "Too High"
        conf_str = f"{confidence:.2f}" if confidence is not None else "N/A"
        print(f" - [{event_id}] {pred_str} (Confidence: {conf_str}) at {time}")

def add_handicapper(
        capper_id: str, 
        capper_name: str, 
        twitter_handle: str, 
        insta_handle: str,
        tiktok_handle: str, 
        image_url: str,
        accuracy_score: float,
        units_per_month: float,
        capper_bio: str,
        capper_pricing: str,
        group_memberships: str,
        estimated_customers: int,
        followers: int,
        last_updated: str

        ):
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            INSERT IGNORE INTO handicappers (capper_id, capper_name, twitter_handle, instagram_handle, 
                       tiktok_handle, image_url, accuracy_score, units_per_month, capper_bio, 
                       capper_pricing, group_memberships, estimated_customers, followers, last_updated)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (capper_id, capper_name, twitter_handle, insta_handle, 
                tiktok_handle, image_url, accuracy_score, units_per_month, capper_bio, 
                capper_pricing, group_memberships, estimated_customers, followers, last_updated))
        
def search_by_name(capper_name: str):
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT capper_id, capper_name, twitter_handle, insta_handle, 
                tiktok_handle, image_url, accuracy_score, units_per_month, capper_bio, 
                capper_pricing, group_memberships, estimated_customers, followers, last_updated
            FROM handicappers
            WHERE capper_name = %s
            ORDER BY capper_name DESC
        """, (capper_name,))
        rows = cursor.fetchall()

def search_by_social_handle(insta_handle: str = None, twitter_handle: str = None, tiktok_handle: str = None):
    query = """
        SELECT capper_id, capper_name, twitter_handle, insta_handle, 
               tiktok_handle, image_url, accuracy_score, units_per_month, 
               capper_bio, capper_pricing, group_memberships, 
               estimated_customers, followers, last_updated
        FROM handicappers
        WHERE
    """
    params = []
    conditions = []

    if insta_handle:
        conditions.append("insta_handle = %s")
        params.append(insta_handle)

    if twitter_handle:
        conditions.append("twitter_handle = %s")
        params.append(twitter_handle)

    if tiktok_handle:
        conditions.append("tiktok_handle = %s")
        params.append(tiktok_handle)

    if not conditions:
        raise ValueError("At least one social media handle must be provided.")

    
    query += " OR ".join(conditions)

    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute(query, tuple(params))
        return cursor.fetchall()


def submit_event(
    event_id: str,
    event_date: str,
    league: str,
    team_a: str,
    team_b: str,
    crowd_probability: float,
    expert_predictions: List[Tuple[str, int, Optional[float]]],
    actual_result: Optional[int] = None,
    pick_type: str = "MoneyLine",
    player_team: str = "None",
    stat_type: str = "MoneyLine"
) -> Tuple[bool, str]:
    try:
        with get_connection() as conn:
            cursor = conn.cursor()

            # Prevent duplicates
            cursor.execute("SELECT 1 FROM events WHERE event_id = %s", (event_id,))
            if cursor.fetchone():
                return False, f"Event '{event_id}' already exists."

            cursor.execute("""
                INSERT INTO events (event_id, event_date, league, team_a, team_b, actual_result, pick_type, player_team, stat_type)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (event_id, event_date, league, team_a, team_b, actual_result, pick_type, player_team, stat_type))

            cursor.execute("""
                INSERT INTO crowd_predictions (event_id, crowd_probability)
                VALUES (%s, %s)
            """, (event_id, crowd_probability))

            for expert_name, prediction, confidence in expert_predictions:
                cursor.execute("""
                    INSERT INTO expert_predictions (event_id, expert_name, prediction, confidence)
                    VALUES (%s, %s, %s, %s)
                """, (event_id, expert_name, prediction, confidence))

                cursor.execute("SELECT 1 FROM expert_reliability WHERE expert_name = %s", (expert_name,))
                if not cursor.fetchone():
                    cursor.execute("INSERT INTO expert_reliability (expert_name) VALUES (%s)", (expert_name,))

        return True, "Event submitted successfully."
    except Exception as e:
        return False, f"Database error: {e}"


def fetch_nba_player_log(player_name: str, game_date: Optional[str] = None):
    with get_connection() as conn:
        cursor = conn.cursor()

        if game_date:
            cursor.execute("""
                SELECT * FROM NBA_Player_Logs
                WHERE player_name = %s AND game_date = %s
            """, (player_name, game_date))
        else:
            cursor.execute("""
                SELECT * FROM NBA_Player_Logs
                WHERE player_name = %s
                ORDER BY game_date DESC
                LIMIT 1
            """, (player_name,))
        
        row = cursor.fetchone()

    if row:
        columns = [desc[0] for desc in cursor.description]
        print(f"\n🧾 Statline for {player_name}:")
        for col, val in zip(columns, row):
            print(f"{col}: {val}")
    else:
        print(f"⚠️ No statline found for {player_name}.")


def bulk_insert_sample_data(conn, sample_events, sample_predictions):
    cursor = conn.cursor()

    # Insert sample events
    event_query = """
        INSERT INTO events (event_id, player_name, league, game_date, stat_type, stat_threshold)
        VALUES (%s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE player_name=VALUES(player_name)
    """
    for event in sample_events:
        cursor.execute(event_query, (
            event["event_id"],
            event["player_name"],
            event["league"],
            event["game_date"],
            event["stat_type"],
            event["stat_threshold"]
        ))

    # Insert sample expert predictions
    prediction_query = """
        INSERT INTO expert_predictions (event_id, expert_name, prediction, confidence)
        VALUES (%s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE confidence=VALUES(confidence)
    """
    for pred in sample_predictions:
        cursor.execute(prediction_query, (
            pred["event_id"],
            pred["expert_name"],
            pred["prediction"],
            pred["confidence"]
        ))

    conn.commit()
    print("✅ Sample events and predictions inserted.")

import random
from datetime import datetime, timedelta
import pymysql
import os

def generate_and_insert_sample_data():
    print("Accessing Database")
    conn = get_connection()

    sample_events = []
    sample_predictions = []

    player_pool = ["Tyler Herro", "Bam Adebayo", "Jayson Tatum", "Jaylen Brown", "Davion Mitchell"]
    leagues = ["NBA"]
    stat_types = ["Points", "Assists", "Rebounds", "Steals", "Blocks"]
    expert_pool = ["ExpertA", "ExpertB", "ExpertC", "ExpertD", "ExpertE"]

    base_date = datetime.today() - timedelta(days=30)

    for i in range(500):
        player_name = random.choice(player_pool)
        league = random.choice(leagues)
        stat_type = random.choice(stat_types)
        stat_threshold = round(random.uniform(5, 30), 1)
        event_date = (base_date + timedelta(days=i % 15)).date()
        event_date_str = event_date.isoformat()

        team_a = "MIA"
        team_b = "BOS"

        event_id = f"{event_date_str}-{league}-{player_name.replace(' ', '').upper()}{stat_threshold}{stat_type.upper()}"

        event = {
            "event_id": event_id,
            "player_name": player_name,
            "league": league,
            "event_date": event_date_str,
            "stat_type": stat_type,
            "stat_threshold": stat_threshold,
            "team_a": team_a,
            "team_b": team_b
        }

        sample_events.append(event)

        for expert in expert_pool:
            prediction = random.choice([0, 1])
            confidence = round(random.uniform(0.5, 1.0), 2)


            sample_predictions.append({
                "event_id": event_id,
                "expert_name": expert,
                "prediction": prediction,
                "confidence": confidence
            })

    bulk_insert_sample_data(conn, sample_events, sample_predictions)
    conn.close()

def bulk_insert_sample_data(conn, sample_events, sample_predictions):
    cursor = conn.cursor()

    event_query = """
        INSERT INTO events (
            event_id, player_name, league, event_date,
            stat_type, stat_threshold, team_a, team_b
        )
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            player_name = VALUES(player_name)
    """

    prediction_query = """
        INSERT INTO expert_predictions (
            event_id, expert_name, prediction, confidence
        )
        VALUES (%s, %s, %s, %s)
    """

    for event in sample_events:
        cursor.execute(event_query, (
            event["event_id"],
            event["player_name"],
            event["league"],
            event["event_date"],
            event["stat_type"],
            event["stat_threshold"],
            event["team_a"],
            event["team_b"]
        ))

    for pred in sample_predictions:
        cursor.execute(prediction_query, (
            pred["event_id"],
            pred["expert_name"],
            pred["prediction"],
            pred["confidence"]
        ))

    conn.commit()
    print(f"✅ Inserted {len(sample_events)} events and {len(sample_predictions)} predictions.")

def get_player_team(event_id):
    # Connect to the MySQL database
    conn = get_connection()
    
    cursor = conn.cursor()
    
    # Query to get the team based on event_id
    query = """
        SELECT player_team
        FROM events
        WHERE event_id = %s
    """
    
    # Execute the query with the provided event_id
    cursor.execute(query, (event_id,))
    
    # Fetch the result
    result = cursor.fetchone()

    # Close the database connection
    cursor.close()
    conn.close()
    
    # If result is found, return the team
    if result:
        return result[0]  # team is in the first column
    else:
        return None  # If no result found for the event_id



def update_game_date_league_team_for_predictions():
    # Connect to the MySQL database
    conn = get_connection()
    cursor = conn.cursor()
    
    # Query to fetch all event_ids
    cursor.execute("SELECT event_id FROM expert_predictions")
    event_ids = cursor.fetchall()
    
    # Loop through each event_id and update game_date, league, and team
    for event_id in event_ids:
        # Extract game_date from event_id (first part before the first hyphen)
        event_parts = event_id[0].split('-')
        game_date = '-'.join(event_parts[:3])  # Extract 'YYYY-MM-DD' part
        league = event_parts[3]  # Extract league (4th part)
        team = get_player_team(event_id)
        
        # Update query to set game_date, league, and team based on event_id
        update_query = """
            UPDATE expert_predictions
            SET game_date = %s, league = %s, team = %s
            WHERE event_id = %s
        """
        cursor.execute(update_query, (game_date, league, team, event_id[0]))
    
    # Commit the changes to the database
    conn.commit()
    
    # Close the database connection
    cursor.close()
    conn.close()
    
    print("Game dates, leagues, and teams updated successfully!")


#fetch_nba_player_log("K. Towns","2024-10-22")
#print("Accessing Database")
# update_game_date_league_team_for_predictions()


def insert_lebron_event_prediction():
    conn = get_connection()
    cursor = conn.cursor()

    event_id = "2024-10-22-NBA-KTOWNS14.0PTS"
    event_date = "2024-10-22"
    league = "NBA"
    team_a = "Over"
    team_b = "Under"
    player_name = "K. Towns"
    stat_type = "points"
    stat_threshold = 14.0
    pick_type = "Prop"
    player_team = "New York Knicks"

    # === Insert into events ===
    cursor.execute("""
        INSERT INTO events (
            event_id, event_date, league, team_a, team_b,
            actual_result, context_features, pick_type,
            player_team, stat_type, player_name, stat_threshold
        ) VALUES (
            %s, %s, %s, %s, %s,
            NULL, NULL, %s,
            %s, %s, %s, %s
        )
        ON DUPLICATE KEY UPDATE player_name = VALUES(player_name)
    """, (
        event_id, event_date, league, team_a, team_b,
        pick_type, player_team, stat_type, player_name, stat_threshold
    ))

    # === Insert into crowd_predictions ===
    crowd_prob = round(random.uniform(0.45, 0.7), 4)
    cursor.execute("""
        INSERT INTO crowd_predictions (event_id, crowd_probability)
        VALUES (%s, %s)
        ON DUPLICATE KEY UPDATE crowd_probability = VALUES(crowd_probability)
    """, (event_id, crowd_prob))

    # === Insert expert_predictions ===
    experts = {
        "Expert A": 0.90,
        "Expert B": 0.80,
        "Expert C": 0.75,
        "Expert D": 0.70,
        "Expert E": 0.55,
    }

    for expert, accuracy in experts.items():
        # Simulate prediction based on accuracy
        prediction = int(random.random() < accuracy)
        confidence = round(random.uniform(0.6, 1.0), 3)
        cursor.execute("""
            INSERT INTO expert_predictions (
                event_id, expert_name, prediction, confidence, stat_threshold
            ) VALUES (%s, %s, %s, %s, %s)
        """, (event_id, expert, prediction, confidence, stat_threshold))

    conn.close()
    print(f"✅ Inserted simulated predictions for event: {event_id}")

#insert_lebron_event_prediction()


def get_player_and_stat_type(event_id):
    # Connect to the MySQL database
    conn =get_connection()
    
    cursor = conn.cursor()
    
    # Query to select player_name and stat_type based on event_id
    query = """
        SELECT player_name, stat_type
        FROM events
        WHERE event_id = %s
    """
    
    # Execute the query
    cursor.execute(query, (event_id,))
    
    # Fetch the result
    result = cursor.fetchone()
    
    # Close the database connection
    cursor.close()
    conn.close()
    
    # Return the result (player_name and stat_type)
    if result:
        return result
    else:
        return None  # Return None if no result is found
    

# event_id = '2024-10-22-NBA-JBRUNSON21.5PTS'
# result = get_player_and_stat_type(event_id)
# if result:
#     print(f"Player Name: {result[0]}, Stat Type: {result[1]}")
# else:
#     print("No data found for the given event_id.")

def get_todays_events_with_handicappers(target_date=None):
    from datetime import datetime
    date_str = target_date or datetime.now().strftime("%Y-%m-%d")
    results = []
    class EventObject:
        def __init__(self, event_id, event_date, player_name, event_team, handicappers, stat_threshold=None, pick_type=None, team_a=None, team_b=None, predictions=None):
            self.event_id = event_id
            self.event_date = event_date
            self.player_name = player_name
            self.event_team = event_team
            self.handicappers = handicappers
            self.stat_threshold = stat_threshold
            self.pick_type = pick_type
            self.team_a = team_a
            self.team_b = team_b
            self.predictions = predictions or []
        def to_dict(self):
            return {
                "event_id": self.event_id,
                "event_date": self.event_date,
                "player_name": self.player_name,
                "event_team": self.event_team,
                "handicappers": self.handicappers,
                "stat_threshold": self.stat_threshold,
                "pick_type": self.pick_type,
                "team_a": self.team_a,
                "team_b": self.team_b,
                "predictions": self.predictions
            }
    conn = get_dict_connection()
    with conn:
        cursor = conn.cursor()
        join_query = (
            """
            SELECT e.event_id, e.event_date, e.team_a AS event_team,
                   ep.player_name, e.stat_threshold, e.pick_type, e.team_a, e.team_b,
                   GROUP_CONCAT(DISTINCT h.name) AS handicappers
            FROM events e
            LEFT JOIN event_picks ep ON e.event_id = ep.event_id
            LEFT JOIN handicappers h ON ep.handicapper_id = h.id
            WHERE e.event_date = %s
            GROUP BY e.event_id, ep.player_name, e.team_a, e.stat_threshold, e.pick_type, e.team_b
            """
        )

        simple_query = (
            """
            SELECT event_id, event_date, player_name, team_a AS event_team,
                   stat_threshold, pick_type, team_a, team_b
            FROM events
            WHERE event_date = %s
            """
        )

        try:
            cursor.execute(join_query, (date_str,))
            rows = cursor.fetchall()
        except Exception as db_err:
            # If the join table doesn't exist (errno 1146) or any other error occurs,
            # gracefully fall back to a simplified query so the API call still works.
            if hasattr(db_err, "args") and len(db_err.args) > 0 and db_err.args[0] == 1146:
                # Missing table, use fallback
                cursor.execute(simple_query, (date_str,))
                rows = cursor.fetchall()
            else:
                raise

        for row in rows:
            handicappers = row.get("handicappers") if "handicappers" in row else None
            if handicappers:
                handicapper_list = [h.strip() for h in handicappers.split(",") if h.strip()]
            else:
                cursor.execute("SELECT DISTINCT expert_name FROM expert_predictions WHERE event_id = %s", (row["event_id"],))
                expert_rows = cursor.fetchall()
                handicapper_list = [er["expert_name"] for er in expert_rows]
            
            cursor.execute("SELECT prediction FROM expert_predictions WHERE event_id = %s", (row["event_id"],))
            prediction_rows = cursor.fetchall()
            predictions_list = [pr["prediction"] for pr in prediction_rows if pr["prediction"] is not None]
            
            obj = EventObject(
                event_id=row["event_id"],
                event_date=row["event_date"],
                player_name=row.get("player_name"),
                event_team=row.get("event_team"),
                handicappers=handicapper_list,
                stat_threshold=row.get("stat_threshold"),
                pick_type=row.get("pick_type"),
                team_a=row.get("team_a"),
                team_b=row.get("team_b"),
                predictions=predictions_list
            )
            results.append(obj)
    return results
